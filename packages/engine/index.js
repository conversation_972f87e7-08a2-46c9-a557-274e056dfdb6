if (__DEV__) {
  import('./ReactotronConfig').then(() => console.log('Reactotron Configured'));
}
/**
 * @format
 */

if (!__DEV__) {
  global.console = {
    info: () => {},
    log: () => {},
    assert: () => {},
    warn: () => {},
    debug: () => {},
    error: () => {},
    time: () => {},
    timeEnd: () => {},
  };
}

import {AppRegistry} from 'react-native';
import App from 'core';
// import {name as appName} from './app.json';

const appName = 'bTeam';

AppRegistry.registerComponent(appName, () => App);
