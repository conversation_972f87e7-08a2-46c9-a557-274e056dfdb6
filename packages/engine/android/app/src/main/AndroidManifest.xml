<manifest xmlns:android="http://schemas.android.com/apk/res/android">
  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.CAMERA" />
  <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
  <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
  <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />

  <application android:name=".MainApplication" android:label="@string/app_name"
    android:icon="@mipmap/ic_launcher" android:roundIcon="@mipmap/ic_launcher_round"
    android:allowBackup="false" android:usesCleartextTraffic="true" android:theme="@style/AppTheme">

    <activity android:name=".MainActivity" android:label="@string/app_name"
      android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
      android:launchMode="singleTask" android:screenOrientation="portrait"
      android:windowSoftInputMode="adjustResize"
      android:exported="true">

      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
        <action android:name="android.intent.action.DOWNLOAD_COMPLETE" />
      </intent-filter>

    </activity>

    <meta-data
      android:name="com.google.android.geo.API_KEY"
      android:value="AIzaSyBlrVljaWAyoy1o8SZbfzXWhkx1dX0xE44" />
    <service android:name="com.asterinet.react.bgactions.RNBackgroundActionsTask"
      android:foregroundServiceType="dataSync" />
  </application>
</manifest>