import React from "react";
import { Animated } from "react-native";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { getHeaderTitle } from "@react-navigation/elements";
import { SCREEN_NAMES } from "../constants/screenNames";
import { useDispatch, useSelector } from "react-redux";
import {
  cancelMultiSelection,
  selectAllEmails,
  unSelectAllEmails,
} from "../slices/generalSlice";
import { fetchGridMessages } from "../slices/gridMessageSlice";
import { addSearchSuggestion } from "../slices/searchSuggestionsSlice";

// Components
import InboxHOC from "../containers/InboxHOC";
import FoldersHOC from "../containers/FoldersHOC";
import MessageHOC from "../containers/MessageHOC";
import RelatedMessagesHOC from "../containers/RelatedMessagesHOC";
import InboxNavigationHeader from "./headers/InboxNavigationHeader";
import MessageNavigationHeader from "./headers/MessageNavigationHeader";
import ComposeMessageHOC from "../containers/ComposeMessageHOC";
import ComposeMessageNavigationHeader from "./headers/ComposeMessageNavigationHeader";
import NavigationHeader from "./headers/NavigationHeader";
import SearchFiltersNavigationHeader from "./headers/SearchFiltersNavigationHeader";
import InboxSearchFiltersHOC from "../containers/InboxSearchFiltersHOC";
import { Message } from "../types/message";
import { FOLDER_NAMES } from "../constants/folderNames";
import { setSearchFilters } from "../slices/searchFiltersSlice";
import { SEARCH_ENTITIES } from "../constants/searchEntities";
import MessagesHOC from "../containers/MessagesHOC";

const Stack = createNativeStackNavigator();

const InboxStack: React.FC = () => {
  const dispatch = useDispatch();
  const {
    isMultiSelectActive,
    sendMessageLoading,
    sendDraftMessageLoading,
    inboxSearchFields,
    selectedMessageIds,
  } = useSelector((state) => state.root.bTeamGeneralSlice);

  const { folders, selectedFolderId, searchFilters } = useSelector(
    (state) => state.persist.gridMessageSlice
  );

  const { searchSuggestions } = useSelector(
    (state) =>
      state.persist.bTeamSearchSuggestionsSlice?.[SEARCH_ENTITIES.messages]
  );

  const { appliedSearchFiltersCount } = useSelector(
    (state) => state.root.bTeamSearchFiltersSlice?.[SEARCH_ENTITIES.messages]
  );

  const messagesById: Message = useSelector(
    (state) => state.persist.gridMessageSlice.gridMessages.byId
  );

  // If the user applies a search filter, we want to display emails from a different list
  const emailIds =
    appliedSearchFiltersCount > 0
      ? folders.byId[selectedFolderId]?.filteredEmailIds
      : folders.byId[selectedFolderId]?.emailIds;

  const cancelMultiSelect = () => {
    dispatch(cancelMultiSelection());
  };

  const selectAll = () => {
    dispatch(selectAllEmails(emailIds));
  };

  const unSelectAll = () => {
    dispatch(unSelectAllEmails());
  };

  const fetchGridMessagesAction = () => dispatch(fetchGridMessages());

  const addSearchSuggestionAction = (suggestion: string) =>
    dispatch(addSearchSuggestion(suggestion));

  const isDeletedFolder =
    folders.allIds.find(
      (id) => folders.byId[id].name === FOLDER_NAMES.deletedItems
    ) === selectedFolderId;

  return (
    <Stack.Navigator
      initialRouteName={SCREEN_NAMES.inbox}
      screenOptions={{
        header: ({ navigation, route, options, back }) => {
          const title = getHeaderTitle(options, route.name);

          return (
            <NavigationHeader
              back={back}
              navigation={navigation}
              title={title}
            />
          );
        },
        presentation: "card",
        animationTypeForReplace: "push",
        animation: "none",
      }}
    >
      <Stack.Screen
        name={SCREEN_NAMES.inbox}
        component={InboxHOC}
        options={{
          header: ({ navigation, route, options, back }) => {
            const title = getHeaderTitle(options, route.name);

            const hidingHeightStyle = {
              transform: [{ translateY: options?.translateY }],
            };

            return (
              <Animated.View
                style={options?.translateY ? hidingHeightStyle : {}}
              >
                <InboxNavigationHeader
                  navigation={navigation}
                  title={folders.byId[selectedFolderId]?.name || title}
                  isMultiSelectActive={isMultiSelectActive}
                  inboxSearchFields={inboxSearchFields}
                  appliedSearchFilters={searchFilters}
                  cancelMultiSelection={cancelMultiSelect}
                  selectAllEmails={selectAll}
                  unSelectAllEmails={unSelectAll}
                  fetchGridMessages={fetchGridMessagesAction}
                  searchSuggestions={searchSuggestions}
                  addSearchSuggestion={addSearchSuggestionAction}
                  selectedMessageIds={selectedMessageIds}
                  emailIds={emailIds}
                />
              </Animated.View>
            );
          },
        }}
      />
      <Stack.Screen name={SCREEN_NAMES.folders} component={FoldersHOC} />

      <Stack.Screen
        name={SCREEN_NAMES.message}
        component={MessagesHOC}
        options={{
          header: ({ navigation, route, options, back }) => {
            const currentUMS_Guid = route.params?.UMS_Guid;
            const message = messagesById?.[currentUMS_Guid];

            return (
              <MessageNavigationHeader
                back={back}
                navigation={navigation}
                isMultiSelectActive={isMultiSelectActive}
                cancelMultiSelection={cancelMultiSelect}
                selectAllEmails={selectAll}
                isFlagged={message?.isFlagged}
                isViewed={message?.isViewed}
                handleDeleteMessage={options?.deleteMessage}
                handleFlagPress={options?.handleFlagMessage}
                handleMarkAsReadUnread={options?.handleMarkAsReadUnread}
                isDeletedFolder={isDeletedFolder}
              />
            );
          },
        }}
      />
      <Stack.Screen
        name={SCREEN_NAMES.composeMessage}
        component={ComposeMessageHOC}
        options={{
          header: ({ navigation, options }) => (
            <ComposeMessageNavigationHeader
              navigation={navigation}
              sendMessage={options?.sendMessage}
              sendMessageLoading={sendMessageLoading}
              draftMessage={options?.sendDraft}
              sendDraftMessageLoading={sendDraftMessageLoading}
              addAttachment={options?.addAttachment}
              uploadAttachmentLoading={options?.uploadAttachmentLoading}
              messageAttachmentsLength={options?.messageAttachmentsLength}
            />
          ),
        }}
      />
      <Stack.Screen
        name={SCREEN_NAMES.inboxSearchFilters}
        component={InboxSearchFiltersHOC}
        options={{
          header: ({ navigation, options, route }) => (
            <SearchFiltersNavigationHeader navigation={navigation} />
          ),
        }}
      />
      <Stack.Screen
        name={SCREEN_NAMES.relatedMessages}
        component={RelatedMessagesHOC}
        options={{
          header: ({ navigation, route }) => (
            <NavigationHeader
              back={true}
              navigation={navigation}
              title="Related Messages"
            />
          ),
        }}
      />
    </Stack.Navigator>
  );
};

export default InboxStack;
