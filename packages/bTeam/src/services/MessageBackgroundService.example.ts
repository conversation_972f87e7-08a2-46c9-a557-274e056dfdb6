/**
 * Example usage of MessageBackgroundService
 * 
 * This file demonstrates how to use the background service for loading
 * non-essential message data (comments, metadata, actions) in the background.
 */

import messageBackgroundService from './MessageBackgroundService';

// Example 1: Load single message data in background
export const loadSingleMessageExample = () => {
  const messageId = 'ae508e75-ce3a-f011-be04-6045bd2aaf50';
  
  // Add message to background queue with high priority
  messageBackgroundService.addMessageToQueue(messageId, 'high');
  
  console.log('✅ Message added to background queue');
};

// Example 2: Preload multiple messages for better UX
export const preloadMessagesExample = () => {
  const messageIds = [
    'ae508e75-ce3a-f011-be04-6045bd2aaf50',
    'a3c52b45-063b-f011-be04-6045bd2aaf50',
    'f0eef0e0-873f-f011-be05-6045bd2aaf50',
  ];
  
  // Add multiple messages with low priority (for preloading)
  messageBackgroundService.addMessagesToQueue(messageIds, 'low');
  
  console.log(`✅ ${messageIds.length} messages added to background queue for preloading`);
};

// Example 3: Check queue status
export const checkQueueStatusExample = () => {
  const status = messageBackgroundService.getQueueStatus();
  
  console.log('📊 Background Queue Status:', {
    queueLength: status.queueLength,
    isRunning: status.isRunning,
    currentTask: status.currentTask,
  });
};

// Example 4: Clear queue when needed
export const clearQueueExample = () => {
  messageBackgroundService.clearQueue();
  console.log('🗑️ Background queue cleared');
};

// Example 5: Stop background processing
export const stopBackgroundProcessingExample = async () => {
  await messageBackgroundService.stopBackgroundProcessing();
  console.log('⏹️ Background processing stopped');
};

/**
 * Complete example of how to use in a React component
 */
export const ReactComponentExample = `
import React, { useEffect } from 'react';
import { useMessageBackgroundLoader } from '../hooks/useMessageBackgroundLoader';

const MessageComponent = ({ messageId, adjacentMessageIds }) => {
  const {
    loadMessageBodyImmediate,
    loadMessageDataBackground,
    preloadMessages,
    clearBackgroundQueue,
  } = useMessageBackgroundLoader();

  useEffect(() => {
    // 1. Load critical data immediately
    loadMessageBodyImmediate(messageId);
    
    // 2. Load non-essential data in background
    loadMessageDataBackground(messageId, 'high');
    
    // 3. Preload adjacent messages
    if (adjacentMessageIds.length > 0) {
      preloadMessages(adjacentMessageIds, 'low');
    }
    
    // 4. Cleanup on unmount
    return () => {
      clearBackgroundQueue();
    };
  }, [messageId]);

  return (
    <div>Your message UI here</div>
  );
};
`;

/**
 * What gets loaded in background:
 * 
 * 1. getMessageComments({ guid: messageId, entityId: 1001 })
 *    - Loads all comments for the message
 *    - Used for displaying comment count and comment list
 * 
 * 2. getMessageMetadata({ guid: messageId })
 *    - Loads message metadata (case info, etc.)
 *    - Used for displaying case information
 * 
 * 3. getMessageActions({ UMS_Guid: messageId })
 *    - Loads available actions for the message
 *    - Used for enabling/disabling action buttons
 * 
 * What does NOT get loaded in background:
 * 
 * - getMessageBody({ UMS_Guid: messageId })
 *   This is critical for UI and should be loaded immediately
 *   using loadMessageBodyImmediate() function
 */
