import BackgroundActions from 'react-native-background-actions';
import store from '../../../core/store/store';
import {
  getMessageComments,
  getMessageMetadata,
  getMessageActions,
} from '../slices/gridMessageSlice';

/**
 * Background service for loading non-essential message data
 *
 * This service handles:
 * - getMessageComments: Loading message comments in background
 * - getMessageMetadata: Loading message metadata in background
 * - getMessageActions: Loading message actions in background
 *
 * It does NOT handle getMessageBody as that's critical for UI and should be loaded immediately.
 *
 * Usage:
 * ```typescript
 * import messageBackgroundService from './MessageBackgroundService';
 *
 * // Add single message to background queue
 * messageBackgroundService.addMessageToQueue('message-id', 'high');
 *
 * // Add multiple messages for preloading
 * messageBackgroundService.addMessagesToQueue(['id1', 'id2'], 'low');
 * ```
 */

interface MessageLoadTask {
  messageId: string;
  entityId?: number;
  priority: 'high' | 'medium' | 'low';
  timestamp: number;
}

class MessageBackgroundService {
  private taskQueue: MessageLoadTask[] = [];
  private isRunning = false;
  private currentTaskId: string | null = null;

  // Background task options
  private backgroundOptions = {
    taskName: 'MessageDataLoader',
    taskTitle: 'Loading message data',
    taskDesc: 'Loading comments, metadata, and actions for messages',
    taskIcon: {
      name: 'ic_launcher',
      type: 'mipmap',
    },
    color: '#ff00ff',
    linkingURI: 'bSeafarer://message',
    parameters: {
      delay: 1000, // 1 second delay between tasks
    },
  };

  /**
   * Add a message to the background loading queue
   */
  public addMessageToQueue(
    messageId: string,
    priority: 'high' | 'medium' | 'low' = 'medium',
    entityId: number = 1001
  ): void {
    // Check if message is already in queue
    const existingTaskIndex = this.taskQueue.findIndex(
      task => task.messageId === messageId
    );

    const newTask: MessageLoadTask = {
      messageId,
      entityId,
      priority,
      timestamp: Date.now(),
    };

    if (existingTaskIndex >= 0) {
      // Update existing task with higher priority if needed
      const existingTask = this.taskQueue[existingTaskIndex];
      if (this.getPriorityValue(priority) > this.getPriorityValue(existingTask.priority)) {
        this.taskQueue[existingTaskIndex] = newTask;
        this.sortQueue();
      }
    } else {
      // Add new task
      this.taskQueue.push(newTask);
      this.sortQueue();
    }

    // Start processing if not already running
    if (!this.isRunning) {
      this.startBackgroundProcessing();
    }
  }

  /**
   * Add multiple messages to the queue (for preloading)
   */
  public addMessagesToQueue(
    messageIds: string[],
    priority: 'high' | 'medium' | 'low' = 'low',
    entityId: number = 1001
  ): void {
    messageIds.forEach(messageId => {
      this.addMessageToQueue(messageId, priority, entityId);
    });
  }

  /**
   * Remove a message from the queue
   */
  public removeMessageFromQueue(messageId: string): void {
    this.taskQueue = this.taskQueue.filter(task => task.messageId !== messageId);
  }

  /**
   * Clear all tasks from the queue
   */
  public clearQueue(): void {
    this.taskQueue = [];
  }

  /**
   * Start background processing
   */
  private async startBackgroundProcessing(): Promise<void> {
    if (this.isRunning) return;

    try {
      this.isRunning = true;

      // Define the background task function
      const backgroundTask = async (taskData: any) => {
        // This function runs in the background
        while (BackgroundActions.isRunning() && this.taskQueue.length > 0) {
          await this.processNextTask();
          await this.delay(taskData.delay || 1000);
        }
      };

      // Start background task
      await BackgroundActions.start(backgroundTask, this.backgroundOptions);

    } catch (error) {
      console.warn('Failed to start background task:', error);
      this.isRunning = false;
    }
  }

  /**
   * Stop background processing
   */
  public async stopBackgroundProcessing(): Promise<void> {
    if (!this.isRunning) return;

    try {
      this.isRunning = false;
      await BackgroundActions.stop();
    } catch (error) {
      console.warn('Failed to stop background task:', error);
    }
  }

  /**
   * Process the next task in the queue
   */
  private async processNextTask(): Promise<void> {
    const task = this.taskQueue.shift();
    if (!task) return;

    try {
      console.log(`🔄 Background loading data for message: ${task.messageId}`);

      // Update background task title
      await BackgroundActions.updateNotification({
        taskTitle: 'Loading message data',
        taskDesc: `Loading data for message ${task.messageId.substring(0, 8)}...`,
      });

      // Dispatch the actions
      await this.loadMessageData(task);

    } catch (error) {
      console.warn(`Failed to load data for message ${task.messageId}:`, error);
    }
  }

  /**
   * Load all non-essential data for a message
   */
  private async loadMessageData(task: MessageLoadTask): Promise<void> {
    const { messageId, entityId } = task;

    // Dispatch actions with small delays to prevent overwhelming the API
    store.dispatch(getMessageComments({ guid: messageId, entityId: entityId || 1001 }));

    await this.delay(200);
    store.dispatch(getMessageMetadata({ guid: messageId }));

    await this.delay(200);
    store.dispatch(getMessageActions({ UMS_Guid: messageId }));
  }

  /**
   * Get priority value for sorting
   */
  private getPriorityValue(priority: 'high' | 'medium' | 'low'): number {
    switch (priority) {
      case 'high': return 3;
      case 'medium': return 2;
      case 'low': return 1;
      default: return 1;
    }
  }

  /**
   * Sort queue by priority and timestamp
   */
  private sortQueue(): void {
    this.taskQueue.sort((a, b) => {
      const priorityDiff = this.getPriorityValue(b.priority) - this.getPriorityValue(a.priority);
      if (priorityDiff !== 0) return priorityDiff;
      return a.timestamp - b.timestamp; // Earlier tasks first for same priority
    });
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get current queue status
   */
  public getQueueStatus(): {
    queueLength: number;
    isRunning: boolean;
    currentTask: string | null;
  } {
    return {
      queueLength: this.taskQueue.length,
      isRunning: this.isRunning,
      currentTask: this.currentTaskId,
    };
  }
}

// Export singleton instance
export const messageBackgroundService = new MessageBackgroundService();
export default messageBackgroundService;
