import React, { useEffect, useCallback, useRef, useState, memo } from "react";
import { <PERSON>, BackHandler, useWindowDimensions, FlatList } from "react-native";
import {
  useRoute,
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native";
import { useThemeAwareObject, Theme } from "b-ui-lib";

// Components
import Message<PERSON><PERSON> from "./MessageHOC";

type Props = {};

// Individual message component - subscribes only to its own data
const MessageItem = memo(
  ({ UMS_Guid, width }: { UMS_Guid: string; width: number }) => {
    return (
      <View style={{ width }}>
        <MessageHOC UMS_Guid={UMS_Guid} />
      </View>
    );
  }
);

// Memoized FlatList - won't re-render when container updates
const MemoizedMessagesList = memo(
  ({
    emailIds,
    initialIndex,
    width,
    onViewableItemsChanged,
  }: {
    emailIds: string[];
    initialIndex: number;
    width: number;
    onViewableItemsChanged;
  }) => {
    const { color } = useThemeAwareObject((theme: Theme) => ({
      color: theme.color,
    }));

    const renderMessageItem = useCallback(
      ({ item: UMS_Guid }: { item: string; index: number }) => (
        <MessageItem UMS_Guid={UMS_Guid} width={width} />
      ),
      [width]
    );

    const keyExtractor = useCallback((item: string) => item, []);

    const getItemLayout = useCallback(
      (_, index: number) => ({
        length: width,
        offset: width * index,
        index,
      }),
      [width]
    );

    return (
      <FlatList
        style={{ flex: 1, backgroundColor: color.BACKGROUND }}
        data={emailIds}
        renderItem={renderMessageItem}
        keyExtractor={keyExtractor}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        initialScrollIndex={initialIndex}
        getItemLayout={getItemLayout}
        maintainVisibleContentPosition={{
          minIndexForVisible: 0,
        }}
        // Optimized for smooth swiping
        windowSize={5}
        maxToRenderPerBatch={3}
        updateCellsBatchingPeriod={300}
        removeClippedSubviews={false}
        initialNumToRender={5}
        onViewableItemsChanged={onViewableItemsChanged}
      />
    );
  }
);

// Container component
const MessagesHOC: React.FC<Props> = () => {
  const route = useRoute();
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const { width } = useWindowDimensions();
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Static data from route params - not subscribing to Redux for container state
  const initialUMS_Guid = route.params?.UMS_Guid;
  const emailIds: string[] = route.params?.emailIds || [];
  const initialIndex = emailIds.findIndex((id) => id === initialUMS_Guid);

  const handleBackPress = useCallback(() => {
    navigation.goBack();
    return true;
  }, [navigation]);

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackPress);

    // Cleanup debounce timer on unmount
    return () => {
      BackHandler.removeEventListener("hardwareBackPress", handleBackPress);
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [handleBackPress]);

  const _onViewableItemsChanged = useCallback((info: any) => {
    console.log("Viewable items changed:", info);
  }, []);

  return (
    <View style={{ flex: 1 }}>
      <MemoizedMessagesList
        emailIds={emailIds}
        initialIndex={initialIndex}
        width={width}
        onViewableItemsChanged={_onViewableItemsChanged}
      />
    </View>
  );
};

export default MessagesHOC;
