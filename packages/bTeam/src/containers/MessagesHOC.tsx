import React, {
  useEffect,
  useMemo,
  useCallback,
  useRef,
  useState,
} from "react";
import { <PERSON>, BackHandler, useWindowDimensions, FlatList } from "react-native";
import ContentLoader, { Rect, Circle } from "react-content-loader/native";
import {
  useRoute,
  NavigationProp,
  ParamListBase,
  useNavigation,
  useFocusEffect,
} from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import {
  clearCopyOrMoveMessageFolderError,
  flagMessages,
  getMessageActions,
  getMessageBody,
  getMessageComments,
  getMessageMetadata,
  performDeleteAction,
  performMarkAsReadUnreadAction,
  performUnDeleteAction,
} from "../slices/gridMessageSlice";
import {
  clearDownloadedAttachments,
  setDownloadedAttachments,
} from "../slices/attachmentsSlice";
import { sendMessageClearStatus } from "../slices/generalSlice";
import { Message } from "../types/message";
import { MessageActionTypes } from "../types/MessageActionTypes";

// Helpers
import {
  downloadMultipleAttachments,
  downloadSingleAttachment,
} from "bcomponents/downloadHelpers/downloadAttachmentHelper";
import { calculateAvatarName } from "../helpers/calculateAvatarName";
// Components
import CommentsHOC from "./CommentsHOC";
import MessageScreen from "../components/message/MessageScreen";
import { useThemeAwareObject, Theme } from "b-ui-lib";

// Import the MessageScreen Props type for better type safety
type MessageScreenProps = React.ComponentProps<typeof MessageScreen>;

// Constants
import { EMAIL_CASES } from "../constants/emailCases";
import { SCREEN_NAMES } from "../constants/screenNames";
import { FILE_DOWNLOAD_STATUS } from "bcomponents";
import { FLAGGED_API_VALUES } from "../constants/apiValues";
import { FOLDER_NAMES } from "../constants/folderNames";

type Props = {};

type FileDownloadStatus =
  (typeof FILE_DOWNLOAD_STATUS)[keyof typeof FILE_DOWNLOAD_STATUS];

const MessagesHOC: React.FC = ({}: Props) => {
  const route = useRoute();
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const { width } = useWindowDimensions();
  const dispatch = useDispatch();
  const initialUMS_Guid = route.params?.UMS_Guid;
  const emailIds: string[] = route.params?.emailIds || [];
  const initialIndex = emailIds.findIndex((id) => id === initialUMS_Guid);
  const [currentEmailIndex, setCurrentEmailIndex] = useState(initialIndex);
  const [isInitialMount, setIsInitialMount] = useState(true);
  const flatListRef = useRef<FlatList>(null);
  const currentUMS_Guid = emailIds[currentEmailIndex];
  const { casesList } = useSelector((state) => state.persist.bTeamCasesSlice);
  const { color } = useThemeAwareObject((theme: Theme) => ({
    color: theme.color,
  }));
  const message: Message = useSelector(
    (state) => state.persist.gridMessageSlice.gridMessages.byId[currentUMS_Guid]
  );

  const messages = useSelector(
    (state) => state.persist.gridMessageSlice.gridMessages
  );

  const { token } = useSelector((state) => state.persist.bTeamAuth);
  const { attachments, downloadedAttachments } = useSelector(
    (state) => state.root.bTeamAttachmentsSlice
  );

  const {
    folders,
    selectedFolderId,
    getMessageBodyLoading,
    getMessageCommentsLoading,
    getMessageActionsLoading,
    getMessageBodyError,
    getMessageCommentsError,
    getMessageActionsError,
    copyOrMoveMessageFolderError,
    messageFolders,
  } = useSelector((state) => state.persist.gridMessageSlice);

  const { sendDraftMessageSuccess } = useSelector(
    (state) => state.root.bTeamGeneralSlice
  );
  const isDeletedFolder =
    folders.allIds.find(
      (id) => folders.byId[id].name === FOLDER_NAMES.deletedItems
    ) === selectedFolderId;

  const cases = useMemo(() => {
    return (
      message?.caseIds
        ?.map((id) => {
          const item = casesList.byId[id];

          return item
            ? {
                id: item.CAS_Guid,
                name: `${item.CAS_Reference} // ${item.CAS_Title}`,
                date: item.CAS_UpdatedTimestamp,
              }
            : null;
        })
        .filter(Boolean) || []
    );
  }, [message?.caseIds, casesList.byId]);

  const handleDeleteMessage = () => {
    dispatch(performDeleteAction({ ids: [currentUMS_Guid] }));
    navigation.goBack();
  };

  const handleUnDeleteMessage = () => {
    dispatch(performUnDeleteAction({ ids: [currentUMS_Guid] }));
    navigation.goBack();
  };

  const handleFlagMessageAction = () => {
    dispatch(
      flagMessages({
        ids: [currentUMS_Guid],
        value: message.isFlagged
          ? FLAGGED_API_VALUES.unFlag
          : FLAGGED_API_VALUES.flag,
      })
    );
  };

  const handleMarkAsReadUnreadAction = () => {
    // If message is viewed (read), mark as unread (mode 7)
    // If message is not viewed (unread), mark as read (mode 6)
    const mode = message?.isViewed ? 7 : 6;
    dispatch(performMarkAsReadUnreadAction({ ids: [currentUMS_Guid], mode }));
  };

  const handleGetMessageActions = () =>
    dispatch(getMessageActions({ UMS_Guid: currentUMS_Guid }));

  const setDownloadedAttachmentsAction = (payload) => {
    dispatch(setDownloadedAttachments(payload));
  };

  const clearDownloadedAttachmentsAction = () => {
    dispatch(clearDownloadedAttachments());
  };

  const clearCopyOrMoveMessageFolderErrorAction = () => {
    dispatch(clearCopyOrMoveMessageFolderError());
  };

  const handleMoveSelectedEmailsToFolder = () => {
    navigation.navigate(SCREEN_NAMES.folders, {
      emailCase: EMAIL_CASES.move,
      isMultiSelect: false,
      messageId: currentUMS_Guid,
    });
  };

  const handleCopySelectedEmailsToFolder = () => {
    navigation.navigate(SCREEN_NAMES.folders, {
      emailCase: EMAIL_CASES.copy,
      isMultiSelect: false,
      messageId: currentUMS_Guid,
    });
  };

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", () => {
      navigation.goBack();
      return true;
    });

    // Cleanup debounce timer on unmount
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [navigation]);

  useEffect(() => {
    navigation.setOptions({
      deleteMessage: isDeletedFolder
        ? handleUnDeleteMessage
        : handleDeleteMessage,
      handleFlagMessage: handleFlagMessageAction,
      handleMarkAsReadUnread: handleMarkAsReadUnreadAction,
    });
  }, [navigation, message, isDeletedFolder]);

  // Initial load for the current message (only on component mount)
  useEffect(() => {
    const initialUMS_Guid = emailIds[initialIndex];
    if (
      !messages.byId[initialUMS_Guid]?.fullMessageBody ||
      messages.byId[initialUMS_Guid]?.inOut === 3
    ) {
      dispatch(getMessageBody({ UMS_Guid: initialUMS_Guid }));
    }

    dispatch(getMessageComments({ guid: initialUMS_Guid, entityId: 1001 }));
    dispatch(getMessageMetadata({ guid: initialUMS_Guid }));
    dispatch(getMessageActions({ UMS_Guid: initialUMS_Guid }));
  }, []); // Only run on mount

  // Handle initial scroll position and clear initial mount state
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsInitialMount(false);

      if (initialIndex > 0 && flatListRef.current) {
        flatListRef.current?.scrollToIndex({
          index: initialIndex,
          animated: false,
        });
      }
    }, 150); // Small delay to ensure FlatList is fully mounted

    return () => clearTimeout(timer);
  }, [initialIndex]);

  useFocusEffect(
    useCallback(() => {
      if (sendDraftMessageSuccess) dispatch(sendMessageClearStatus());
    }, [sendDraftMessageSuccess])
  );

  useEffect(() => {
    dispatch(
      performMarkAsReadUnreadAction({
        ids: [currentUMS_Guid],
        mode: 6,
      })
    );
  }, [currentUMS_Guid]);

  const repliesData = useMemo(
    () =>
      message?.repliedByUser
        ? [
            {
              id: 1,
              avatarName: calculateAvatarName(message?.repliedByUser),
              from: "",
              username: message?.repliedByUser,
              date: message?.timeReplied,
            },
          ]
        : [],
    [message?.repliedByUser, message?.timeReplied]
  );

  const handleFilesDownloadStatus = (
    attachmentId: string,
    downloadStatus: FileDownloadStatus
  ) => {
    setDownloadedAttachmentsAction({
      id: attachmentId,
      status: downloadStatus,
      isLoading: downloadStatus === FILE_DOWNLOAD_STATUS.loading,
    });
  };

  const handleDownloadAttachment = async (attachmentId: string) => {
    clearDownloadedAttachments();

    await downloadSingleAttachment(
      token,
      attachmentId,
      attachments?.byId?.[attachmentId]?.name,
      handleFilesDownloadStatus
    );
  };

  const renderMessageItem = useCallback(
    ({ item: UMS_Guid, index }) => {
      console.log("renderMessageItem", UMS_Guid);
      const itemMessage = messages.byId[UMS_Guid];

      // Show skeleton for missing messages OR if loading state is active
      if (
        !itemMessage ||
        !itemMessage.fullMessageBody ||
        (getMessageBodyLoading[UMS_Guid] && !itemMessage.fullMessageBody)
      ) {
        return <MessageSkeleton width={width} />;
      }

      // Common props used by both dummy and full messages
      const commonMessageProps = {
        width,
        message: itemMessage,
        replies: repliesData,
        messageAttachmentsIds: itemMessage?.attachmentsIds,
        attachments,
        attachmentsCount: itemMessage?.attachmentsCount,
        downloadedAttachments,
        foldersIds: itemMessage?.foldersIds,
        messageFolders,
        caseMetadata: itemMessage?.messageMetadata,
        cases,
        getMessageBodyLoading: getMessageBodyLoading[UMS_Guid] || false,
        getMessageCommentsLoading,
        getMessageActionsLoading,
        messageBodyError: getMessageBodyError,
        getMessageCommentsError,
        getMessageActionsError,
        copyOrMoveMessageFolderError,
        isAnyFileDownloadLoading,
        isDraft: itemMessage?.inOut === 3,
        ...staticHandlers,
      };

      // For current message, render full version with CommentsHOC
      return (
        <View style={{ width }}>
          <CommentsHOC
            guid={UMS_Guid}
            entityId="1001"
            umsGuidToSendComment={UMS_Guid}
            commentIds={itemMessage?.messageCommentIds}
            refreshListCallback={() =>
              dispatch(
                getMessageComments({
                  guid: UMS_Guid,
                  entityId: 1001,
                })
              )
            }
          >
            {React.createElement(MessageScreen, {
              ...commonMessageProps,
              isDummy: false,
            } as unknown as MessageScreenProps)}
          </CommentsHOC>
        </View>
      );
    },
    [
      currentEmailIndex,
      width,
      messages.byId,
      repliesData,
      attachments,
      downloadedAttachments,
      messageFolders,
      cases,
      getMessageBodyLoading,
      getMessageCommentsLoading,
      getMessageActionsLoading,
      getMessageBodyError,
      getMessageCommentsError,
      getMessageActionsError,
      copyOrMoveMessageFolderError,
      isAnyFileDownloadLoading,
      staticHandlers,
    ]
  );

  return (
    <FlatList
      style={{ flex: 1, backgroundColor: color.BACKGROUND }}
      ref={flatListRef}
      data={emailIds}
      renderItem={renderMessageItem}
      keyExtractor={(item) => item}
      horizontal
      pagingEnabled
      showsHorizontalScrollIndicator={false}
      initialScrollIndex={initialIndex}
      getItemLayout={(_, index) => ({
        length: width,
        offset: width * index,
        index,
      })}
      onScrollToIndexFailed={(info) => {
        const wait = new Promise((resolve) => setTimeout(resolve, 500));
        wait.then(() => {
          flatListRef.current?.scrollToIndex({
            index: info.index,
            animated: true,
          });
        });
      }}
      onViewableItemsChanged={handleViewableItemsChanged}
      viewabilityConfig={viewabilityConfig}
      maintainVisibleContentPosition={{
        minIndexForVisible: 0,
      }}
      // Optimized for smooth swiping - render exactly 5 items at a time
      windowSize={8}
      maxToRenderPerBatch={8}
      updateCellsBatchingPeriod={300}
      removeClippedSubviews={false}
      initialNumToRender={8}
    />
  );
};

export default MessagesHOC;
