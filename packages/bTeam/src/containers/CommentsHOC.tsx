import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  cloneElement,
  useMemo,
} from "react";
import { Alert } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import uuid from "react-native-uuid";

// Import the comment-related actions and helpers
import {
  postMessageComment,
  postMessageCommentSuccessDismiss,
  starMessageComment,
  getMessageCommentUsers,
} from "../slices/generalSlice";
import { uploadAttachmentHelper } from "../helpers/postAttachment";

interface CommentsHOCProps {
  guid: string;
  entityId: string;
  umsGuidToSendComment: string;
  commentIds: string[];
  children: React.ReactElement<any>;
  refreshListCallback: () => void;
}

const CommentsHOC: React.FC<CommentsHOCProps> = ({
  guid,
  entityId,
  commentIds,
  umsGuidToSendComment,
  children,
  refreshListCallback,
}) => {
  const dispatch = useDispatch();
  const { comments } = useSelector((state) => state.persist.bTeamCommentsSlice);

  const {
    commentMessageUsers,
    getMessageCommentUsersLoading,
    getMessageCommentUsersError,
    postMessageCommentLoading,
    postMessageCommentError,
    postMessageCommentSuccess,
  } = useSelector((state: any) => state.root.bTeamGeneralSlice);

  const { participantUsers } = useSelector(
    (state) => state.root.bTeamGeneralNotificationSlice
  );
  // Local state for attachment uploads within comments
  const [uploadAttachmentLoading, setUploadAttachmentLoading] = useState(false);
  const [uploadAttachmentError, setUploadAttachmentError] = useState("");
  const [commentAttachments, setCommentAttachments] = useState<string[]>([]);
  const [totalAttachmentSize, setTotalAttachmentSize] = useState(0);

  // Create a unique file upload ID on mount.
  const FILE_UPLOAD_ID = useRef<string | null>(null);
  useEffect(() => {
    FILE_UPLOAD_ID.current = uuid.v4() as string;
  }, []);

  const fetchCommentNotifiedUsers = (commentGuid) => {
    dispatch(
      getMessageCommentUsers({
        guid: commentGuid,
        entityId: 1006,
      })
    );
  };

  // Compute the effective UMS_Guid based on the entity type.
  const effectiveUMSGuid =
    entityId === "1001" || entityId === 1001
      ? umsGuidToSendComment || guid
      : guid;

  // Function to post a new comment.
  const handlePostNewComment = useCallback(
    ({
      CMM_CMM_Guid,
      notifyUsers,
      description,
      restricted,
    }: {
      CMM_CMM_Guid: string | null; // Allow null for memo-type new comments
      notifyUsers: string[];
      description: string;
      restricted: 0 | 1;
    }) => {
      if (!notifyUsers || notifyUsers.length === 0) {
        Alert.alert(
          "No Recipients Selected",
          "Please select at least one recipient before posting your comment."
        );
        return;
      }

      dispatch(
        postMessageComment({
          UMS_Guid: effectiveUMSGuid,
          CMM_CMM_Guid: CMM_CMM_Guid!,
          CMM_ENT_Id: entityId,
          notifyUsers,
          description,
          restricted,
          commentAttachments,
          FLN_EntityPK_Guid_Temp: FILE_UPLOAD_ID.current,
        })
      );

      // Trigger a refresh of comments after posting.
      setTimeout(() => {
        refreshListCallback();
      }, 1);
    },
    [
      commentAttachments,
      dispatch,
      effectiveUMSGuid,
      entityId,
      refreshListCallback,
    ]
  );

  const handlePostMessageCommentSuccessDismiss = useCallback(() => {
    setCommentAttachments([]);
    dispatch(postMessageCommentSuccessDismiss());
  }, [dispatch]);

  const handleStarComment = useCallback(
    (id: string, currentStarStatus: boolean) => {
      const newStarValue = !comments.byId[id].isStarred;

      // Single action handles both optimistic update and API call
      dispatch(
        starMessageComment({
          id,
          value: newStarValue,
          guid: effectiveUMSGuid,
          entityId,
        })
      );

      setTimeout(() => {
        refreshListCallback();
      }, 1);
    },
    [dispatch, effectiveUMSGuid, comments.byId, refreshListCallback]
  );

  const { token, domainBaseUrl } = useSelector(
    (state: any) => state.persist.bTeamAuth
  );
  const handleAddAttachment = useCallback(async () => {
    setUploadAttachmentLoading(true);
    setUploadAttachmentError("");

    await uploadAttachmentHelper(
      token,
      FILE_UPLOAD_ID.current!,
      1006,
      totalAttachmentSize,
      (FLN_Guid: string, documentSize: number) => {
        setCommentAttachments((prev) => [...prev, FLN_Guid]);
        setTotalAttachmentSize((prev) => prev + documentSize);
        setUploadAttachmentLoading(false);
      },
      (error: string) => {
        setUploadAttachmentLoading(false);
        setUploadAttachmentError(error);
      },
      domainBaseUrl
    );
  }, [token, totalAttachmentSize]);

  // Memoize the mapped comments to avoid recalculation when data hasn't changed
  const mappedComments = useMemo(() => {
    const mapCommentReplies = (commentId: string) => ({
      ...comments.byId[commentId],
      replies: commentIds
        ?.filter(
          (id) => comments.byId[id]?.parentId === comments.byId[commentId].id
        )
        .map(mapCommentReplies),
    });

    return commentIds
      ?.filter((id) => comments.byId[id]?.parentId === null)
      ?.map(mapCommentReplies);
  }, [commentIds, comments.byId]);

  // Memoize the mapped participant users to avoid recalculation when data hasn't changed
  const mappedParticipantUsers = useMemo(() => {
    return participantUsers.allIds.map((userId, index) => ({
      id: participantUsers.byId[userId].id,
      value: participantUsers.byId[userId].username,
      avatarName: participantUsers.byId[userId].userAbbreviation
        .charAt(0)
        .toUpperCase(),
      avatarBackgroundColor: index % 2 === 0 ? "#FFB7CD" : "#FF8833",
    }));
  }, [participantUsers.allIds, participantUsers.byId]);

  // Memoize the comments props to avoid recreating the object unnecessarily
  const commentsProps = useMemo(() => ({
    comments: mappedComments,
    recipientsEmails: mappedParticipantUsers,
    notifiedUsers: commentMessageUsers,
    postMessageCommentLoading,
    postMessageCommentError,
    postMessageCommentSuccess,
    postNewComment: handlePostNewComment,
    handlePostMessageCommentSuccessDismiss,
    handleStarComment,
    handleAddAttachment,
    uploadAttachmentLoading,
    uploadAttachmentError,
    attachmentsLength: commentAttachments.length,
    fetchNotifiedUsers: fetchCommentNotifiedUsers,
    fetchNotifiedUsersLoading: getMessageCommentUsersLoading,
    fetchNotifiedUsersError: getMessageCommentUsersError,
  }), [
    mappedComments,
    mappedParticipantUsers,
    commentMessageUsers,
    postMessageCommentLoading,
    postMessageCommentError,
    postMessageCommentSuccess,
    handlePostNewComment,
    handlePostMessageCommentSuccessDismiss,
    handleStarComment,
    handleAddAttachment,
    uploadAttachmentLoading,
    uploadAttachmentError,
    commentAttachments.length,
    fetchCommentNotifiedUsers,
    getMessageCommentUsersLoading,
    getMessageCommentUsersError,
  ]);

  return cloneElement(children, { ...commentsProps });
};

export default CommentsHOC;
