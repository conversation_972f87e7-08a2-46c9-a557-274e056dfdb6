import React, {
  useEffect,
  useMemo,
  useCallback,
  useState,
  useRef,
} from "react";
import { <PERSON>, BackHandler, useWindowDimensions, FlatList } from "react-native";
import ContentLoader, { Rect, Circle } from "react-content-loader/native";
import {
  useRoute,
  NavigationProp,
  ParamListBase,
  useNavigation,
  useFocusEffect,
} from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import {
  clearCopyOrMoveMessageFolderError,
  flagMessages,
  getMessageActions,
  getMessageBody,
  getMessageComments,
  getMessageMetadata,
  performDeleteAction,
  performMarkAsReadUnreadAction,
  performUnDeleteAction,
} from "../slices/gridMessageSlice";
import {
  clearDownloadedAttachments,
  setDownloadedAttachments,
} from "../slices/attachmentsSlice";
import { sendMessageClearStatus } from "../slices/generalSlice";
import { Message } from "../types/message";
import { MessageActionTypes } from "../types/MessageActionTypes";

// Helpers
import {
  downloadMultipleAttachments,
  downloadSingleAttachment,
} from "../../../bcomponents/downloadHelpers/downloadAttachmentHelper";
import { calculateAvatarName } from "../helpers/calculateAvatarName";
// Components
import CommentsHOC from "./CommentsHOC";
import MessageScreen from "../components/message/MessageScreen";
import { useThemeAwareObject, Theme } from "b-ui-lib";
import { TabCacheProvider } from "../components/message/tabs/context/TabCacheContext";

// Import the MessageScreen Props type for better type safety
type MessageScreenProps = React.ComponentProps<typeof MessageScreen>;

// Constants
import { EMAIL_CASES } from "../constants/emailCases";
import { SCREEN_NAMES } from "../constants/screenNames";
import { FILE_DOWNLOAD_STATUS } from "bcomponents";
import { FLAGGED_API_VALUES } from "../constants/apiValues";
import { FOLDER_NAMES } from "../constants/folderNames";

type Props = {};

type FileDownloadStatus =
  (typeof FILE_DOWNLOAD_STATUS)[keyof typeof FILE_DOWNLOAD_STATUS];

// Message skeleton component for loading states
const MessageSkeleton: React.FC<{ width: number }> = ({ width }) => {
  const { color } = useThemeAwareObject((theme: Theme) => ({
    color: theme.color,
  }));
  const spacing = 16; // SPACING.M equivalent

  return (
    <View
      style={{
        width,
        backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
        flex: 1,
      }}
    >
      <ContentLoader
        width={width}
        height={800}
        speed={1}
        backgroundColor={color.SKELETON_BACKGROUND}
        foregroundColor={color.SKELETON_FOREGROUND}
      >
        {/* MessageInfoHeader - Subject and date */}
        <Rect
          x={spacing}
          y={spacing}
          rx="4"
          ry="4"
          width={width * 0.7}
          height="18"
        />
        <Rect
          x={spacing}
          y={spacing + 25}
          rx="4"
          ry="4"
          width={width * 0.4}
          height="14"
        />

        {/* MessageInfoData - Avatar, username, email details */}
        <Circle cx={spacing + 20} cy={spacing + 70} r="20" />
        <Rect
          x={spacing + 50}
          y={spacing + 55}
          rx="4"
          ry="4"
          width={width * 0.3}
          height="14"
        />
        <Rect
          x={spacing + 50}
          y={spacing + 75}
          rx="4"
          ry="4"
          width={width * 0.5}
          height="12"
        />
        <Rect
          x={spacing + 50}
          y={spacing + 92}
          rx="4"
          ry="4"
          width={width * 0.4}
          height="12"
        />

        {/* Tab bar with 5 tabs - matches actual TabBarNavigator */}
        <Rect
          x="0"
          y={spacing + 130}
          rx="0"
          ry="0"
          width={width / 5}
          height="75"
        />
        <Rect
          x={width / 5}
          y={spacing + 130}
          rx="0"
          ry="0"
          width={width / 5}
          height="75"
        />
        <Rect
          x={(width / 5) * 2}
          y={spacing + 130}
          rx="0"
          ry="0"
          width={width / 5}
          height="75"
        />
        <Rect
          x={(width / 5) * 3}
          y={spacing + 130}
          rx="0"
          ry="0"
          width={width / 5}
          height="75"
        />
        <Rect
          x={(width / 5) * 4}
          y={spacing + 130}
          rx="0"
          ry="0"
          width={width / 5}
          height="75"
        />

        {/* Nested tabs (Details/Replies) */}
        <Rect
          x={spacing}
          y={spacing + 225}
          rx="4"
          ry="4"
          width={80}
          height="16"
        />
        <Rect
          x={width - spacing - 120}
          y={spacing + 225}
          rx="4"
          ry="4"
          width={120}
          height="16"
        />

        {/* Content area - simulating HTML content */}
        <Rect
          x={spacing}
          y={spacing + 265}
          rx="4"
          ry="4"
          width={width * 0.95}
          height="14"
        />
        <Rect
          x={spacing}
          y={spacing + 285}
          rx="4"
          ry="4"
          width={width * 0.8}
          height="14"
        />
        <Rect
          x={spacing}
          y={spacing + 305}
          rx="4"
          ry="4"
          width={width * 0.9}
          height="14"
        />
        <Rect
          x={spacing}
          y={spacing + 325}
          rx="4"
          ry="4"
          width={width * 0.75}
          height="14"
        />
        <Rect
          x={spacing}
          y={spacing + 345}
          rx="4"
          ry="4"
          width={width * 0.85}
          height="14"
        />
        <Rect
          x={spacing}
          y={spacing + 365}
          rx="4"
          ry="4"
          width={width * 0.7}
          height="14"
        />
        <Rect
          x={spacing}
          y={spacing + 385}
          rx="4"
          ry="4"
          width={width * 0.6}
          height="14"
        />

        {/* Bottom action buttons - Reply, Reply All, Forward */}
        <Rect x="0" y={750} rx="0" ry="0" width={width / 3} height="50" />
        <Rect
          x={width / 3}
          y={750}
          rx="0"
          ry="0"
          width={width / 3}
          height="50"
        />
        <Rect
          x={(width / 3) * 2}
          y={750}
          rx="0"
          ry="0"
          width={width / 3}
          height="50"
        />
      </ContentLoader>
    </View>
  );
};

const MessageHOC: React.FC = ({}: Props) => {
  const route = useRoute();
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const { width } = useWindowDimensions();
  const dispatch = useDispatch();
  const UMS_Guid = (route.params as any)?.UMS_Guid;
  const emailIds = (route.params as any)?.emailIds || [];
  const { casesList } = useSelector((state) => state.persist.bTeamCasesSlice);

  // FlatList and current message state
  const flatListRef = useRef<FlatList>(null);
  const [currentUMS_Guid, setCurrentUMS_Guid] = useState(UMS_Guid);

  // Find current message index in emailIds array
  const currentMessageIndex = useMemo(() => {
    return emailIds.findIndex((id: string) => id === currentUMS_Guid);
  }, [emailIds, currentUMS_Guid]);

  // Debounced loading timer ref
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const { color } = useThemeAwareObject((theme: Theme) => ({
    color: theme.color,
  }));
  const message: Message = useSelector(
    (state) => state.persist.gridMessageSlice.gridMessages.byId[UMS_Guid]
  );

  const messages = useSelector(
    (state) => state.persist.gridMessageSlice.gridMessages
  );

  const { token } = useSelector((state) => state.persist.bTeamAuth);
  const { attachments, downloadedAttachments } = useSelector(
    (state) => state.root.bTeamAttachmentsSlice
  );

  const {
    folders,
    selectedFolderId,
    getMessageBodyLoading,
    getMessageCommentsLoading,
    getMessageActionsLoading,
    getMessageBodyError,
    getMessageCommentsError,
    getMessageActionsError,
    copyOrMoveMessageFolderError,
    messageFolders,
  } = useSelector((state) => state.persist.gridMessageSlice);

  const { sendDraftMessageSuccess } = useSelector(
    (state) => state.root.bTeamGeneralSlice
  );
  const isDeletedFolder = useMemo(
    () =>
      folders.allIds.find(
        (id) => folders.byId[id].name === FOLDER_NAMES.deletedItems
      ) === selectedFolderId,
    [folders.allIds, folders.byId, selectedFolderId]
  );

  const cases = useMemo(() => {
    return (
      message?.caseIds
        ?.map((id: string) => {
          const item = casesList.byId[id];

          return item
            ? {
                id: item.CAS_Guid,
                name: `${item.CAS_Reference} // ${item.CAS_Title}`,
                date: item.CAS_UpdatedTimestamp,
              }
            : null;
        })
        .filter(Boolean) || []
    );
  }, [message?.caseIds, casesList.byId]);

  const handleDeleteMessage = () => {
    dispatch(performDeleteAction({ ids: [UMS_Guid] }));
    navigation.goBack();
  };

  const handleUnDeleteMessage = () => {
    dispatch(performUnDeleteAction({ ids: [UMS_Guid] }));
    navigation.goBack();
  };

  const handleFlagMessageAction = () => {
    dispatch(
      flagMessages({
        ids: [UMS_Guid],
        value: message.isFlagged
          ? FLAGGED_API_VALUES.unFlag
          : FLAGGED_API_VALUES.flag,
      })
    );
  };

  const handleMarkAsReadUnreadAction = () => {
    // If message is viewed (read), mark as unread (mode 7)
    // If message is not viewed (unread), mark as read (mode 6)
    const mode = message?.isViewed ? 7 : 6;
    dispatch(performMarkAsReadUnreadAction({ ids: [UMS_Guid], mode }));
  };

  const handleGetMessageActions = () =>
    dispatch(getMessageActions({ UMS_Guid }));

  const setDownloadedAttachmentsAction = (payload: any) => {
    dispatch(setDownloadedAttachments(payload));
  };

  const clearDownloadedAttachmentsAction = () => {
    dispatch(clearDownloadedAttachments());
  };

  const clearCopyOrMoveMessageFolderErrorAction = () => {
    dispatch(clearCopyOrMoveMessageFolderError());
  };

  const handleMoveSelectedEmailsToFolder = () => {
    navigation.navigate(SCREEN_NAMES.folders, {
      emailCase: EMAIL_CASES.move,
      isMultiSelect: false,
      messageId: UMS_Guid,
    });
  };

  const handleCopySelectedEmailsToFolder = () => {
    navigation.navigate(SCREEN_NAMES.folders, {
      emailCase: EMAIL_CASES.copy,
      isMultiSelect: false,
      messageId: UMS_Guid,
    });
  };

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", () => {
      navigation.goBack();
      return true;
    });
  }, [navigation]);

  const navigationOptions = useMemo(
    () => ({
      deleteMessage: isDeletedFolder
        ? handleUnDeleteMessage
        : handleDeleteMessage,
      handleFlagMessage: handleFlagMessageAction,
      handleMarkAsReadUnread: handleMarkAsReadUnreadAction,
    }),
    [isDeletedFolder, message]
  );

  useEffect(() => {
    navigation.setOptions(navigationOptions);
  }, [navigation, navigationOptions]);

  // Initial load for the current message (only on component mount)
  useEffect(() => {
    if (
      !messages.byId[UMS_Guid]?.fullMessageBody ||
      messages.byId[UMS_Guid]?.inOut === 3
    ) {
      dispatch(getMessageBody({ UMS_Guid }));
    }
    dispatch(getMessageComments({ guid: UMS_Guid, entityId: 1001 }));
    dispatch(getMessageMetadata({ guid: UMS_Guid }));
    dispatch(getMessageActions({ UMS_Guid }));
  }, []); // Only run on mount

  useFocusEffect(
    useCallback(() => {
      if (sendDraftMessageSuccess) dispatch(sendMessageClearStatus());
    }, [sendDraftMessageSuccess])
  );

  useEffect(() => {
    dispatch(
      performMarkAsReadUnreadAction({
        ids: [UMS_Guid],
        mode: 6,
      })
    );
  }, [UMS_Guid]);

  const repliesData = useMemo(
    () =>
      message?.repliedByUser
        ? [
            {
              id: 1,
              avatarName: calculateAvatarName(message?.repliedByUser),
              from: "",
              username: message?.repliedByUser,
              date: message?.timeReplied,
            },
          ]
        : [],
    [message?.repliedByUser, message?.timeReplied]
  );

  const handleFilesDownloadStatus = (
    attachmentId: string,
    downloadStatus: FileDownloadStatus
  ) => {
    setDownloadedAttachmentsAction({
      id: attachmentId,
      status: downloadStatus,
      isLoading: downloadStatus === FILE_DOWNLOAD_STATUS.loading,
    });
  };

  const handleDownloadAttachment = async (attachmentId: string) => {
    clearDownloadedAttachments();

    await downloadSingleAttachment(
      token,
      attachmentId,
      attachments?.byId?.[attachmentId]?.name,
      handleFilesDownloadStatus
    );
  };

  const fileNamesMapping = useMemo(() => {
    const mapping: Record<string, string> = {};
    if (attachments?.byId) {
      Object.keys(attachments.byId).forEach((id) => {
        mapping[id] = attachments.byId[id].name;
      });
    }
    return mapping;
  }, [attachments?.byId]);

  const handleDownloadAllAttachments = async (attachmentIds: string[]) => {
    // Clear any existing downloaded attachments.
    clearDownloadedAttachments();

    // If there are no attachments, we can optionally return or alert.
    if (attachmentIds.length === 0) {
      console.log("No attachments found");
      return;
    }

    // Download all attachments.
    await downloadMultipleAttachments(
      token,
      attachmentIds,
      fileNamesMapping,
      handleFilesDownloadStatus
    );
  };

  const isAnyFileDownloadLoading = useMemo(
    () =>
      Object.values(downloadedAttachments).some(
        (status) => status === FILE_DOWNLOAD_STATUS.loading
      ),
    [downloadedAttachments]
  );

  const handleEditDraftMessage = () => {
    navigation.navigate(SCREEN_NAMES.composeMessage, {
      UMS_Guid,
      messageType: MessageActionTypes.DraftEdit,
    });
  };

  const handleOnPressReply = () => {
    navigation.navigate(SCREEN_NAMES.composeMessage, {
      UMS_Guid,
      messageType: MessageActionTypes.Reply,
    });
  };

  const handleOnPressReplyAll = () => {
    navigation.navigate(SCREEN_NAMES.composeMessage, {
      UMS_Guid,
      messageType: MessageActionTypes.ReplyAll,
    });
  };

  const handleOnPressForward = () => {
    navigation.navigate(SCREEN_NAMES.composeMessage, {
      UMS_Guid,
      messageType: MessageActionTypes.Forward,
    });
  };

  const handleRetryMessageBody = () => {
    dispatch(getMessageBody({ UMS_Guid }));
  };

  // Debounced message loading function
  const loadMessageData = useCallback((messageId: string) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(() => {
      const itemMessage = messages.byId[messageId];

      if (
        !itemMessage?.fullMessageBody ||
        itemMessage?.inOut === 3
      ) {
        dispatch(getMessageBody({ UMS_Guid: messageId }));
      }
      dispatch(getMessageComments({ guid: messageId, entityId: 1001 }));
      dispatch(getMessageMetadata({ guid: messageId }));
      dispatch(getMessageActions({ UMS_Guid: messageId }));
    }, 300);
  }, [dispatch, messages.byId]);

  // Clean up debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);



  // Memoize static handler props to prevent unnecessary re-renders
  const staticHandlers = useMemo(
    () => ({
      moveToFolder: handleMoveSelectedEmailsToFolder,
      copyToFolder: handleCopySelectedEmailsToFolder,
      getMessageActions: handleGetMessageActions,
      handleRetryMessageBody,
      setDownloadedAttachments: setDownloadedAttachmentsAction,
      clearDownloadedAttachments: clearDownloadedAttachmentsAction,
      handleClearCopyOrMoveMessageFolderError:
        clearCopyOrMoveMessageFolderErrorAction,
      handleDownloadAttachment,
      handleDownloadAllAttachments,
      handleEditDraftMessage,
      onPressReply: handleOnPressReply,
      onPressReplyAll: handleOnPressReplyAll,
      onPressForward: handleOnPressForward,
    }),
    [
      handleMoveSelectedEmailsToFolder,
      handleCopySelectedEmailsToFolder,
      handleGetMessageActions,
      handleRetryMessageBody,
      setDownloadedAttachmentsAction,
      clearDownloadedAttachmentsAction,
      clearCopyOrMoveMessageFolderErrorAction,
      handleDownloadAttachment,
      handleDownloadAllAttachments,
      handleEditDraftMessage,
      handleOnPressReply,
      handleOnPressReplyAll,
      handleOnPressForward,
    ]
  );

  // Render function for FlatList items
  const renderMessage = useCallback(({ item: messageId }: { item: string; index: number }) => {
    const itemMessage = messages.byId[messageId];
    const isCurrentMessage = messageId === currentUMS_Guid;

    // Show skeleton for missing messages
    if (!itemMessage || !itemMessage.fullMessageBody) {
      return (
        <View style={{ width }}>
          <MessageSkeleton width={width} />
        </View>
      );
    }

    // Memoized props for current message
    const messageProps = {
      width,
      message: itemMessage,
      replies: itemMessage?.repliedByUser
        ? [
            {
              id: 1,
              avatarName: calculateAvatarName(itemMessage?.repliedByUser),
              from: "",
              username: itemMessage?.repliedByUser,
              date: itemMessage?.timeReplied,
            },
          ]
        : [],
      messageAttachmentsIds: itemMessage?.attachmentsIds,
      attachments,
      attachmentsCount: itemMessage?.attachmentsCount,
      downloadedAttachments,
      foldersIds: itemMessage?.foldersIds,
      messageFolders,
      caseMetadata: itemMessage?.messageMetadata,
      cases: itemMessage?.caseIds
        ?.map((id: string) => {
          const item = casesList.byId[id];
          return item
            ? {
                id: item.CAS_Guid,
                name: `${item.CAS_Reference} // ${item.CAS_Title}`,
                date: item.CAS_UpdatedTimestamp,
              }
            : null;
        })
        .filter(Boolean) || [],
      getMessageBodyLoading: getMessageBodyLoading[messageId] || false,
      getMessageCommentsLoading,
      getMessageActionsLoading,
      messageBodyError: getMessageBodyError,
      getMessageCommentsError,
      getMessageActionsError,
      copyOrMoveMessageFolderError,
      isAnyFileDownloadLoading,
      isDraft: itemMessage?.inOut === 3,
      isDummy: !isCurrentMessage, // Non-current messages are dummy
      ...staticHandlers,
    };

    return (
      <View style={{ width }}>
        {isCurrentMessage ? (
          <CommentsHOC
            guid={messageId}
            entityId="1001"
            umsGuidToSendComment={messageId}
            commentIds={itemMessage?.messageCommentIds}
            refreshListCallback={() =>
              dispatch(
                getMessageComments({
                  guid: messageId,
                  entityId: 1001,
                })
              )
            }
          >
            {React.createElement(MessageScreen, {
              ...messageProps,
            } as unknown as MessageScreenProps)}
          </CommentsHOC>
        ) : (
          React.createElement(MessageScreen, {
            ...messageProps,
          } as unknown as MessageScreenProps)
        )}
      </View>
    );
  }, [width, messages.byId, currentUMS_Guid, attachments, downloadedAttachments,
      messageFolders, casesList.byId, getMessageBodyLoading, getMessageCommentsLoading,
      getMessageActionsLoading, getMessageBodyError, getMessageCommentsError,
      getMessageActionsError, copyOrMoveMessageFolderError, isAnyFileDownloadLoading,
      staticHandlers, dispatch]);

  // Handle viewable items change with debounced loading
  const handleViewableItemsChanged = useCallback(({ viewableItems }: { viewableItems: any[] }) => {
    if (viewableItems.length > 0) {
      const visibleMessageId = viewableItems[0].item;
      if (visibleMessageId !== currentUMS_Guid) {
        setCurrentUMS_Guid(visibleMessageId);

        // Update navigation params to reflect current message
        navigation.setParams({ UMS_Guid: visibleMessageId });

        // Load message data with debouncing
        loadMessageData(visibleMessageId);

        // Preload adjacent messages
        const currentIndex = emailIds.findIndex((id: string) => id === visibleMessageId);
        if (currentIndex > 0) {
          loadMessageData(emailIds[currentIndex - 1]);
        }
        if (currentIndex < emailIds.length - 1) {
          loadMessageData(emailIds[currentIndex + 1]);
        }
      }
    }
  }, [currentUMS_Guid, emailIds, loadMessageData, navigation]);

  // GetItemLayout for performance optimization
  const getItemLayout = useCallback((_data: ArrayLike<string> | null | undefined, index: number) => ({
    length: width,
    offset: width * index,
    index,
  }), [width]);

  // If no emailIds available, show single message (fallback)
  if (!emailIds || emailIds.length === 0) {
    // Show skeleton for missing messages OR if loading state is active
    if (
      !message ||
      !message.fullMessageBody ||
      (getMessageBodyLoading[UMS_Guid] && !message.fullMessageBody)
    ) {
      return (
        <TabCacheProvider>
          <MessageSkeleton width={width} />
        </TabCacheProvider>
      );
    }

    // Common props used by the message (fallback single message mode)
    const commonMessageProps = {
      width,
      message,
      replies: repliesData,
      messageAttachmentsIds: message?.attachmentsIds,
      attachments,
      attachmentsCount: message?.attachmentsCount,
      downloadedAttachments,
      foldersIds: message?.foldersIds,
      messageFolders,
      caseMetadata: message?.messageMetadata,
      cases,
      getMessageBodyLoading: getMessageBodyLoading[UMS_Guid] || false,
      getMessageCommentsLoading,
      getMessageActionsLoading,
      messageBodyError: getMessageBodyError,
      getMessageCommentsError,
      getMessageActionsError,
      copyOrMoveMessageFolderError,
      isAnyFileDownloadLoading,
      isDraft: message?.inOut === 3,
      ...staticHandlers,
    };

    return (
      <TabCacheProvider>
        <View style={{ flex: 1, backgroundColor: color.BACKGROUND }}>
          <CommentsHOC
            guid={UMS_Guid}
            entityId="1001"
            umsGuidToSendComment={UMS_Guid}
            commentIds={message?.messageCommentIds}
            refreshListCallback={() =>
              dispatch(
                getMessageComments({
                  guid: UMS_Guid,
                  entityId: 1001,
                })
              )
            }
          >
            {React.createElement(MessageScreen, {
              ...commonMessageProps,
              isDummy: false,
            } as unknown as MessageScreenProps)}
          </CommentsHOC>
        </View>
      </TabCacheProvider>
    );
  }

  return (
    <TabCacheProvider>
      <View style={{ flex: 1, backgroundColor: color.BACKGROUND }}>
        <FlatList
          ref={flatListRef}
          data={emailIds}
          renderItem={renderMessage}
          keyExtractor={(item: string) => item}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          getItemLayout={getItemLayout}
          initialScrollIndex={currentMessageIndex >= 0 ? currentMessageIndex : 0}
          onViewableItemsChanged={handleViewableItemsChanged}
          viewabilityConfig={{
            itemVisiblePercentThreshold: 50,
          }}
          windowSize={11}
          maxToRenderPerBatch={3}
          removeClippedSubviews={false}
          disableVirtualization={true}
          initialNumToRender={emailIds.length < 3 ? emailIds.length : 3}
        />
      </View>
    </TabCacheProvider>
  );
};

export default MessageHOC;
