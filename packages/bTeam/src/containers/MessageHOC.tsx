import React, {
  useEffect,
  useMemo,
  useCallback,
  useState,
  useRef,
} from "react";
import { <PERSON>, BackHandler, useWindowDimensions, FlatList } from "react-native";
import ContentLoader, { Rect, Circle } from "react-content-loader/native";
import {
  useRoute,
  NavigationProp,
  ParamListBase,
  useNavigation,
  useFocusEffect,
} from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import {
  clearCopyOrMoveMessageFolderError,
  flagMessages,
  getMessageActions,
  getMessageBody,
  getMessageComments,
  getMessageMetadata,
  performDeleteAction,
  performMarkAsReadUnreadAction,
  performUnDeleteAction,
} from "../slices/gridMessageSlice";
import {
  clearDownloadedAttachments,
  setDownloadedAttachments,
} from "../slices/attachmentsSlice";
import { sendMessageClearStatus } from "../slices/generalSlice";
import { Message } from "../types/message";
import { MessageActionTypes } from "../types/MessageActionTypes";

// Helpers
import {
  downloadMultipleAttachments,
  downloadSingleAttachment,
} from "../../../bcomponents/downloadHelpers/downloadAttachmentHelper";
import { calculateAvatarName } from "../helpers/calculateAvatarName";
// Components
import CommentsHOC from "./CommentsHOC";
import MessageScreen from "../components/message/MessageScreen";
import { useThemeAwareObject, Theme } from "b-ui-lib";
import { TabCacheProvider } from "../components/message/tabs/context/TabCacheContext";

// Import the MessageScreen Props type for better type safety
type MessageScreenProps = React.ComponentProps<typeof MessageScreen>;


// Constants
import { EMAIL_CASES } from "../constants/emailCases";
import { SCREEN_NAMES } from "../constants/screenNames";
import { FILE_DOWNLOAD_STATUS } from "bcomponents";
import { FLAGGED_API_VALUES } from "../constants/apiValues";
import { FOLDER_NAMES } from "../constants/folderNames";

type Props = {};

type FileDownloadStatus =
  (typeof FILE_DOWNLOAD_STATUS)[keyof typeof FILE_DOWNLOAD_STATUS];

// Message skeleton component for loading states
const MessageSkeleton: React.FC<{ width: number }> = ({ width }) => {
  const { color } = useThemeAwareObject((theme: Theme) => ({
    color: theme.color,
  }));
  const spacing = 16; // SPACING.M equivalent

  return (
    <View
      style={{
        width,
        backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
        flex: 1,
      }}
    >
      <ContentLoader
        width={width}
        height={800}
        speed={1}
        backgroundColor={color.SKELETON_BACKGROUND}
        foregroundColor={color.SKELETON_FOREGROUND}
      >
        {/* MessageInfoHeader - Subject and date */}
        <Rect
          x={spacing}
          y={spacing}
          rx="4"
          ry="4"
          width={width * 0.7}
          height="18"
        />
        <Rect
          x={spacing}
          y={spacing + 25}
          rx="4"
          ry="4"
          width={width * 0.4}
          height="14"
        />

        {/* MessageInfoData - Avatar, username, email details */}
        <Circle cx={spacing + 20} cy={spacing + 70} r="20" />
        <Rect
          x={spacing + 50}
          y={spacing + 55}
          rx="4"
          ry="4"
          width={width * 0.3}
          height="14"
        />
        <Rect
          x={spacing + 50}
          y={spacing + 75}
          rx="4"
          ry="4"
          width={width * 0.5}
          height="12"
        />
        <Rect
          x={spacing + 50}
          y={spacing + 92}
          rx="4"
          ry="4"
          width={width * 0.4}
          height="12"
        />

        {/* Tab bar with 5 tabs - matches actual TabBarNavigator */}
        <Rect
          x="0"
          y={spacing + 130}
          rx="0"
          ry="0"
          width={width / 5}
          height="75"
        />
        <Rect
          x={width / 5}
          y={spacing + 130}
          rx="0"
          ry="0"
          width={width / 5}
          height="75"
        />
        <Rect
          x={(width / 5) * 2}
          y={spacing + 130}
          rx="0"
          ry="0"
          width={width / 5}
          height="75"
        />
        <Rect
          x={(width / 5) * 3}
          y={spacing + 130}
          rx="0"
          ry="0"
          width={width / 5}
          height="75"
        />
        <Rect
          x={(width / 5) * 4}
          y={spacing + 130}
          rx="0"
          ry="0"
          width={width / 5}
          height="75"
        />

        {/* Nested tabs (Details/Replies) */}
        <Rect
          x={spacing}
          y={spacing + 225}
          rx="4"
          ry="4"
          width={80}
          height="16"
        />
        <Rect
          x={width - spacing - 120}
          y={spacing + 225}
          rx="4"
          ry="4"
          width={120}
          height="16"
        />

        {/* Content area - simulating HTML content */}
        <Rect
          x={spacing}
          y={spacing + 265}
          rx="4"
          ry="4"
          width={width * 0.95}
          height="14"
        />
        <Rect
          x={spacing}
          y={spacing + 285}
          rx="4"
          ry="4"
          width={width * 0.8}
          height="14"
        />
        <Rect
          x={spacing}
          y={spacing + 305}
          rx="4"
          ry="4"
          width={width * 0.9}
          height="14"
        />
        <Rect
          x={spacing}
          y={spacing + 325}
          rx="4"
          ry="4"
          width={width * 0.75}
          height="14"
        />
        <Rect
          x={spacing}
          y={spacing + 345}
          rx="4"
          ry="4"
          width={width * 0.85}
          height="14"
        />
        <Rect
          x={spacing}
          y={spacing + 365}
          rx="4"
          ry="4"
          width={width * 0.7}
          height="14"
        />
        <Rect
          x={spacing}
          y={spacing + 385}
          rx="4"
          ry="4"
          width={width * 0.6}
          height="14"
        />

        {/* Bottom action buttons - Reply, Reply All, Forward */}
        <Rect x="0" y={750} rx="0" ry="0" width={width / 3} height="50" />
        <Rect
          x={width / 3}
          y={750}
          rx="0"
          ry="0"
          width={width / 3}
          height="50"
        />
        <Rect
          x={(width / 3) * 2}
          y={750}
          rx="0"
          ry="0"
          width={width / 3}
          height="50"
        />
      </ContentLoader>
    </View>
  );
};

const MessageHOC: React.FC = ({}: Props) => {
  const route = useRoute();
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const { width } = useWindowDimensions();
  const dispatch = useDispatch();
  const UMS_Guid = (route.params as any)?.UMS_Guid;
  const emailIds = (route.params as any)?.emailIds || [];
  const { casesList } = useSelector((state) => state.persist.bTeamCasesSlice);

  // FlatList and current message state
  const flatListRef = useRef<FlatList>(null);
  const [currentUMS_Guid, setCurrentUMS_Guid] = useState(UMS_Guid);
  const [isInitialized, setIsInitialized] = useState(false);

  // Find current message index in emailIds array (removed memoization to fix stale index)
  const currentMessageIndex = emailIds.findIndex((id: string) => id === currentUMS_Guid);

  // Reset state when route param changes (new navigation)
  useEffect(() => {
    if (UMS_Guid && UMS_Guid !== currentUMS_Guid) {
      // console.log('🔄 Route param changed, resetting state:', { from: currentUMS_Guid, to: UMS_Guid });
      setCurrentUMS_Guid(UMS_Guid);
      setIsInitialized(false); // Reset initialization flag
    }
  }, [UMS_Guid, currentUMS_Guid]);

  // Debounced loading timer ref
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const { color } = useThemeAwareObject((theme: Theme) => ({
    color: theme.color,
  }));
  const message: Message = useSelector(
    (state) => state.persist.gridMessageSlice.gridMessages.byId[currentUMS_Guid]
  );

  const messages = useSelector(
    (state) => state.persist.gridMessageSlice.gridMessages
  );

  const { token } = useSelector((state) => state.persist.bTeamAuth);
  const { attachments, downloadedAttachments } = useSelector(
    (state) => state.root.bTeamAttachmentsSlice
  );

  const {
    folders,
    selectedFolderId,
    getMessageBodyLoading,
    getMessageCommentsLoading,
    getMessageActionsLoading,
    getMessageBodyError,
    getMessageCommentsError,
    getMessageActionsError,
    copyOrMoveMessageFolderError,
    messageFolders,
  } = useSelector((state) => state.persist.gridMessageSlice);

  const { sendDraftMessageSuccess } = useSelector(
    (state) => state.root.bTeamGeneralSlice
  );
  const isDeletedFolder = useMemo(
    () =>
      folders.allIds.find(
        (id) => folders.byId[id].name === FOLDER_NAMES.deletedItems
      ) === selectedFolderId,
    [folders.allIds, folders.byId, selectedFolderId]
  );

  const cases = useMemo(() => {
    return (
      message?.caseIds
        ?.map((id: string) => {
          const item = casesList.byId[id];

          return item
            ? {
                id: item.CAS_Guid,
                name: `${item.CAS_Reference} // ${item.CAS_Title}`,
                date: item.CAS_UpdatedTimestamp,
              }
            : null;
        })
        .filter(Boolean) || []
    );
  }, [message?.caseIds, casesList.byId]);

  const handleDeleteMessage = () => {
    dispatch(performDeleteAction({ ids: [UMS_Guid] }));
    navigation.goBack();
  };

  const handleUnDeleteMessage = () => {
    dispatch(performUnDeleteAction({ ids: [UMS_Guid] }));
    navigation.goBack();
  };

  const handleFlagMessageAction = () => {
    dispatch(
      flagMessages({
        ids: [UMS_Guid],
        value: message.isFlagged
          ? FLAGGED_API_VALUES.unFlag
          : FLAGGED_API_VALUES.flag,
      })
    );
  };

  const handleMarkAsReadUnreadAction = () => {
    // If message is viewed (read), mark as unread (mode 7)
    // If message is not viewed (unread), mark as read (mode 6)
    const mode = message?.isViewed ? 7 : 6;
    dispatch(performMarkAsReadUnreadAction({ ids: [UMS_Guid], mode }));
  };

  const handleGetMessageActions = () =>
    dispatch(getMessageActions({ UMS_Guid }));

  const setDownloadedAttachmentsAction = (payload: any) => {
    dispatch(setDownloadedAttachments(payload));
  };

  const clearDownloadedAttachmentsAction = () => {
    dispatch(clearDownloadedAttachments());
  };

  const clearCopyOrMoveMessageFolderErrorAction = () => {
    dispatch(clearCopyOrMoveMessageFolderError());
  };

  const handleMoveSelectedEmailsToFolder = () => {
    navigation.navigate(SCREEN_NAMES.folders, {
      emailCase: EMAIL_CASES.move,
      isMultiSelect: false,
      messageId: UMS_Guid,
    });
  };

  const handleCopySelectedEmailsToFolder = () => {
    navigation.navigate(SCREEN_NAMES.folders, {
      emailCase: EMAIL_CASES.copy,
      isMultiSelect: false,
      messageId: UMS_Guid,
    });
  };

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", () => {
      navigation.goBack();
      return true;
    });
  }, [navigation]);

  const navigationOptions = useMemo(
    () => ({
      deleteMessage: isDeletedFolder
        ? handleUnDeleteMessage
        : handleDeleteMessage,
      handleFlagMessage: handleFlagMessageAction,
      handleMarkAsReadUnread: handleMarkAsReadUnreadAction,
    }),
    [isDeletedFolder, message]
  );

  useEffect(() => {
    navigation.setOptions(navigationOptions);
  }, [navigation, navigationOptions]);

  // Initial load for the current message (only on component mount)
  useEffect(() => {
    if (UMS_Guid) {
      // IMMEDIATE: Load essential data
      loadMessageBodyImmediate(UMS_Guid);

      // BACKGROUND: Load non-essential data immediately
      loadMessageDataBackground(UMS_Guid);
    }
  }, []); // Only run on mount

  useFocusEffect(
    useCallback(() => {
      if (sendDraftMessageSuccess) dispatch(sendMessageClearStatus());
    }, [sendDraftMessageSuccess])
  );

  useEffect(() => {
    if (currentUMS_Guid) {
      dispatch(
        performMarkAsReadUnreadAction({
          ids: [currentUMS_Guid],
          mode: 6,
        })
      );
    }
  }, [currentUMS_Guid, dispatch]);

  const repliesData = useMemo(
    () =>
      message?.repliedByUser
        ? [
            {
              id: 1,
              avatarName: calculateAvatarName(message?.repliedByUser),
              from: "",
              username: message?.repliedByUser,
              date: message?.timeReplied,
            },
          ]
        : [],
    [message?.repliedByUser, message?.timeReplied]
  );

  const handleFilesDownloadStatus = (
    attachmentId: string,
    downloadStatus: FileDownloadStatus
  ) => {
    setDownloadedAttachmentsAction({
      id: attachmentId,
      status: downloadStatus,
      isLoading: downloadStatus === FILE_DOWNLOAD_STATUS.loading,
    });
  };

  const handleDownloadAttachment = async (attachmentId: string) => {
    clearDownloadedAttachments();

    await downloadSingleAttachment(
      token,
      attachmentId,
      attachments?.byId?.[attachmentId]?.name,
      handleFilesDownloadStatus
    );
  };

  const fileNamesMapping = useMemo(() => {
    const mapping: Record<string, string> = {};
    if (attachments?.byId) {
      Object.keys(attachments.byId).forEach((id) => {
        mapping[id] = attachments.byId[id].name;
      });
    }
    return mapping;
  }, [attachments?.byId]);

  const handleDownloadAllAttachments = async (attachmentIds: string[]) => {
    // Clear any existing downloaded attachments.
    clearDownloadedAttachments();

    // If there are no attachments, we can optionally return or alert.
    if (attachmentIds.length === 0) {
      console.log("No attachments found");
      return;
    }

    // Download all attachments.
    await downloadMultipleAttachments(
      token,
      attachmentIds,
      fileNamesMapping,
      handleFilesDownloadStatus
    );
  };

  const isAnyFileDownloadLoading = useMemo(
    () =>
      Object.values(downloadedAttachments).some(
        (status) => status === FILE_DOWNLOAD_STATUS.loading
      ),
    [downloadedAttachments]
  );

  const handleEditDraftMessage = () => {
    navigation.navigate(SCREEN_NAMES.composeMessage, {
      UMS_Guid,
      messageType: MessageActionTypes.DraftEdit,
    });
  };

  const handleOnPressReply = () => {
    navigation.navigate(SCREEN_NAMES.composeMessage, {
      UMS_Guid,
      messageType: MessageActionTypes.Reply,
    });
  };

  const handleOnPressReplyAll = () => {
    navigation.navigate(SCREEN_NAMES.composeMessage, {
      UMS_Guid,
      messageType: MessageActionTypes.ReplyAll,
    });
  };

  const handleOnPressForward = () => {
    navigation.navigate(SCREEN_NAMES.composeMessage, {
      UMS_Guid,
      messageType: MessageActionTypes.Forward,
    });
  };

  const handleRetryMessageBody = () => {
    dispatch(getMessageBody({ UMS_Guid: currentUMS_Guid }));
  };

  // Immediate loading for essential data (message body)
  const loadMessageBodyImmediate = useCallback((messageId: string) => {
    const itemMessage = messages.byId[messageId];
    if (!itemMessage?.fullMessageBody || itemMessage?.inOut === 3) {
      dispatch(getMessageBody({ UMS_Guid: messageId }));
    }
  }, [dispatch, messages.byId]);

  // Background loading for non-essential data
  const loadMessageDataBackground = useCallback((messageId: string) => {
    if (!messageId) return;

    try {
      // Always load comments - they're essential for the app functionality
      setTimeout(() => {
        dispatch(getMessageComments({ guid: messageId, entityId: 1001 }));
      }, 50);

      // Always load metadata - contains attachment info
      setTimeout(() => {
        dispatch(getMessageMetadata({ guid: messageId }));
      }, 100);

      // Always load actions
      setTimeout(() => {
        dispatch(getMessageActions({ UMS_Guid: messageId }));
      }, 150);
    } catch (error) {
      console.warn('Background loading failed:', error);
    }
  }, [dispatch]);

  // Preload adjacent messages in background
  const preloadAdjacentMessages = useCallback(async (currentId: string) => {
    if (!currentId || !emailIds?.length) return;

    const currentIndex = emailIds.findIndex((id: string) => id === currentId);
    if (currentIndex === -1) return;

    const adjacentIds = [
      emailIds[currentIndex - 1],
      emailIds[currentIndex + 1]
    ].filter(Boolean);

    if (adjacentIds.length === 0) return;

    // Simple timeout-based preloading
    setTimeout(() => {
      adjacentIds.forEach((messageId, index) => {
        const message = messages.byId[messageId];
        if (!message?.fullMessageBody) {
          setTimeout(() => {
            dispatch(getMessageBody({ UMS_Guid: messageId }));
          }, (index + 1) * 300); // Stagger the calls
        }
      });
    }, 1000); // Start preloading after 1 second
  }, [dispatch, messages.byId, emailIds]);

  // Initialize FlatList positioning after component mount
  useEffect(() => {
    if (emailIds.length > 0 && currentMessageIndex >= 0) {
      // Give FlatList time to render and scroll to initial position
      const initTimer = setTimeout(() => {
        // console.log('✅ FlatList initialized, enabling viewable item changes');
        setIsInitialized(true);
      }, 500); // Wait 500ms for FlatList to settle

      return () => clearTimeout(initTimer);
    }
  }, [emailIds.length, currentMessageIndex]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);


  // Memoize static handler props to prevent unnecessary re-renders
  const staticHandlers = useMemo(
    () => ({
      moveToFolder: handleMoveSelectedEmailsToFolder,
      copyToFolder: handleCopySelectedEmailsToFolder,
      getMessageActions: handleGetMessageActions,
      handleRetryMessageBody,
      setDownloadedAttachments: setDownloadedAttachmentsAction,
      clearDownloadedAttachments: clearDownloadedAttachmentsAction,
      handleClearCopyOrMoveMessageFolderError:
        clearCopyOrMoveMessageFolderErrorAction,
      handleDownloadAttachment,
      handleDownloadAllAttachments,
      handleEditDraftMessage,
      onPressReply: handleOnPressReply,
      onPressReplyAll: handleOnPressReplyAll,
      onPressForward: handleOnPressForward,
    }),
    [
      handleMoveSelectedEmailsToFolder,
      handleCopySelectedEmailsToFolder,
      handleGetMessageActions,
      handleRetryMessageBody,
      setDownloadedAttachmentsAction,
      clearDownloadedAttachmentsAction,
      clearCopyOrMoveMessageFolderErrorAction,
      handleDownloadAttachment,
      handleDownloadAllAttachments,
      handleEditDraftMessage,
      handleOnPressReply,
      handleOnPressReplyAll,
      handleOnPressForward,
    ]
  );

  // Render function for FlatList items
  const renderMessage = useCallback(
    ({ item: messageId }: { item: string; index: number }) => {
      const itemMessage = messages.byId[messageId];
      const isCurrentMessage = messageId === currentUMS_Guid;

      // Show skeleton for missing messages
      if (!itemMessage || !itemMessage.fullMessageBody) {
        return (
          <View style={{ width }}>
            <MessageSkeleton width={width} />
          </View>
        );
      }

      // Memoized props for current message
      const messageProps = {
        width,
        message: itemMessage,
        replies: itemMessage?.repliedByUser
          ? [
              {
                id: 1,
                avatarName: calculateAvatarName(itemMessage?.repliedByUser),
                from: "",
                username: itemMessage?.repliedByUser,
                date: itemMessage?.timeReplied,
              },
            ]
          : [],
        messageAttachmentsIds: itemMessage?.attachmentsIds,
        attachments,
        attachmentsCount: itemMessage?.attachmentsCount,
        downloadedAttachments,
        foldersIds: itemMessage?.foldersIds,
        messageFolders,
        caseMetadata: itemMessage?.messageMetadata,
        cases:
          itemMessage?.caseIds
            ?.map((id: string) => {
              const item = casesList.byId[id];
              return item
                ? {
                    id: item.CAS_Guid,
                    name: `${item.CAS_Reference} // ${item.CAS_Title}`,
                    date: item.CAS_UpdatedTimestamp,
                  }
                : null;
            })
            .filter(Boolean) || [],
        getMessageBodyLoading: getMessageBodyLoading[messageId] || false,
        getMessageCommentsLoading,
        getMessageActionsLoading,
        messageBodyError: getMessageBodyError,
        getMessageCommentsError,
        getMessageActionsError,
        copyOrMoveMessageFolderError,
        isAnyFileDownloadLoading,
        isDraft: itemMessage?.inOut === 3,
        isDummy: !isCurrentMessage, // Non-current messages are dummy
        ...staticHandlers,
      };

      return (
        <View style={{ width }}>
          {isCurrentMessage ? (
            <CommentsHOC
              guid={messageId}
              entityId="1001"
              umsGuidToSendComment={messageId}
              commentIds={itemMessage?.messageCommentIds}
              refreshListCallback={() =>
                dispatch(
                  getMessageComments({
                    guid: messageId,
                    entityId: 1001,
                  })
                )
              }
            >
              {React.createElement(MessageScreen, {
                ...messageProps,
              } as unknown as MessageScreenProps)}
            </CommentsHOC>
          ) : (
            React.createElement(MessageScreen, {
              ...messageProps,
            } as unknown as MessageScreenProps)
          )}
        </View>
      );
    },
    [
      width,
      messages.byId,
      currentUMS_Guid,
      attachments,
      downloadedAttachments,
      messageFolders,
      casesList.byId,
      getMessageBodyLoading,
      getMessageCommentsLoading,
      getMessageActionsLoading,
      getMessageBodyError,
      getMessageCommentsError,
      getMessageActionsError,
      copyOrMoveMessageFolderError,
      isAnyFileDownloadLoading,
      staticHandlers,
      dispatch,
    ]
  );

  // Handle viewable items change with debounced loading
  const handleViewableItemsChanged = useCallback(
    ({ viewableItems }: { viewableItems: any[] }) => {
      // Don't respond to viewable changes until FlatList is properly initialized
      if (!isInitialized) {
        return;
      }

      if (viewableItems?.length > 0) {
        const visibleMessageId = viewableItems[0]?.item;
        if (visibleMessageId && visibleMessageId !== currentUMS_Guid) {
          // console.log('🔄 Swiped to message:', visibleMessageId);
          setCurrentUMS_Guid(visibleMessageId);

          // Update navigation params to reflect current message
          try {
            navigation.setParams({ UMS_Guid: visibleMessageId });
          } catch (error) {
            console.warn('Failed to update navigation params:', error);
          }

          // IMMEDIATE: Load essential data (message body) on UI thread
          loadMessageBodyImmediate(visibleMessageId);

          // BACKGROUND: Load non-essential data in background thread
          loadMessageDataBackground(visibleMessageId);

          // BACKGROUND: Preload adjacent messages
          preloadAdjacentMessages(visibleMessageId);
        }
      }
    },
    [currentUMS_Guid, emailIds, loadMessageBodyImmediate, loadMessageDataBackground, preloadAdjacentMessages, navigation, isInitialized]
  );

  // GetItemLayout for performance optimization
  const getItemLayout = useCallback(
    (_data: ArrayLike<string> | null | undefined, index: number) => ({
      length: width,
      offset: width * index,
      index,
    }),
    [width]
  );

  // If no emailIds available, show single message (fallback)
  if (!emailIds || emailIds.length === 0) {
    // Show skeleton for missing messages OR if loading state is active
    if (
      !message ||
      !message.fullMessageBody ||
      (getMessageBodyLoading[currentUMS_Guid] && !message.fullMessageBody)
    ) {
      return (
        <TabCacheProvider>
          <MessageSkeleton width={width} />
        </TabCacheProvider>
      );
    }

    // Common props used by the message (fallback single message mode)
    const commonMessageProps = {
      width,
      message,
      replies: repliesData,
      messageAttachmentsIds: message?.attachmentsIds,
      attachments,
      attachmentsCount: message?.attachmentsCount,
      downloadedAttachments,
      foldersIds: message?.foldersIds,
      messageFolders,
      caseMetadata: message?.messageMetadata,
      cases,
      getMessageBodyLoading: getMessageBodyLoading[currentUMS_Guid] || false,
      getMessageCommentsLoading,
      getMessageActionsLoading,
      messageBodyError: getMessageBodyError,
      getMessageCommentsError,
      getMessageActionsError,
      copyOrMoveMessageFolderError,
      isAnyFileDownloadLoading,
      isDraft: message?.inOut === 3,
      ...staticHandlers,
    };

    return (
      <TabCacheProvider>
        <View style={{ flex: 1, backgroundColor: color.BACKGROUND }}>
          <CommentsHOC
            guid={currentUMS_Guid}
            entityId="1001"
            umsGuidToSendComment={currentUMS_Guid}
            commentIds={message?.messageCommentIds}
            refreshListCallback={() =>
              dispatch(
                getMessageComments({
                  guid: currentUMS_Guid,
                  entityId: 1001,
                })
              )
            }
          >
            {React.createElement(MessageScreen, {
              ...commonMessageProps,
              isDummy: false,
            } as unknown as MessageScreenProps)}
          </CommentsHOC>
        </View>
      </TabCacheProvider>
    );
  }

  return (
    <TabCacheProvider>
      <View style={{ flex: 1, backgroundColor: color.BACKGROUND }}>
        <FlatList
          ref={flatListRef}
          data={emailIds}
          renderItem={renderMessage}
          keyExtractor={(item: string) => item}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          getItemLayout={getItemLayout}
          initialScrollIndex={
            currentMessageIndex >= 0 ? currentMessageIndex : 0
          }
          onViewableItemsChanged={handleViewableItemsChanged}
          viewabilityConfig={{
            itemVisiblePercentThreshold: 50,
            minimumViewTime: 100, // Require 100ms view time before triggering
          }}
          windowSize={5}
          maxToRenderPerBatch={5}
          removeClippedSubviews={false}
          disableVirtualization={true}
          initialNumToRender={emailIds.length < 5 ? emailIds.length : 5}
        />
      </View>
    </TabCacheProvider>
  );
};

export default MessageHOC;
