import React, {
  useEffect,
  useMemo,
  useCallback,
  useRef,
  useState,
} from "react";
import { <PERSON>, BackHandler, useWindowDimensions, FlatList } from "react-native";
import ContentLoader, { Rect, Circle } from "react-content-loader/native";
import {
  useRoute,
  NavigationProp,
  ParamListBase,
  useNavigation,
  useFocusEffect,
} from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import {
  clearCopyOrMoveMessageFolderError,
  flagMessages,
  getMessageActions,
  getMessageBody,
  getMessageComments,
  getMessageMetadata,
  performDeleteAction,
  performMarkAsReadUnreadAction,
  performUnDeleteAction,
} from "../slices/gridMessageSlice";
import {
  clearDownloadedAttachments,
  setDownloadedAttachments,
} from "../slices/attachmentsSlice";
import { sendMessageClearStatus } from "../slices/generalSlice";
import { Message } from "../types/message";
import { MessageActionTypes } from "../types/MessageActionTypes";

// Helpers
import {
  downloadMultipleAttachments,
  downloadSingleAttachment,
} from "../../../bcomponents/downloadHelpers/downloadAttachmentHelper";
import { calculateAvatarName } from "../helpers/calculateAvatarName";
// Components
import CommentsHOC from "./CommentsHOC";
import MessageScreen from "../components/message/MessageScreen";
import { useThemeAwareObject, Theme } from "b-ui-lib";
import { TabCacheProvider } from "../components/message/tabs/context/TabCacheContext";

// Import the MessageScreen Props type for better type safety
type MessageScreenProps = React.ComponentProps<typeof MessageScreen>;

// Constants
import { EMAIL_CASES } from "../constants/emailCases";
import { SCREEN_NAMES } from "../constants/screenNames";
import { FILE_DOWNLOAD_STATUS } from "bcomponents";
import { FLAGGED_API_VALUES } from "../constants/apiValues";
import { FOLDER_NAMES } from "../constants/folderNames";

type Props = {};

type FileDownloadStatus =
  (typeof FILE_DOWNLOAD_STATUS)[keyof typeof FILE_DOWNLOAD_STATUS];

// Message skeleton component for loading states
const MessageSkeleton: React.FC<{ width: number }> = ({ width }) => {
  const { color } = useThemeAwareObject((theme: Theme) => ({
    color: theme.color,
  }));
  const spacing = 16; // SPACING.M equivalent

  return (
    <View
      style={{
        width,
        backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
        flex: 1,
      }}
    >
      <ContentLoader
        width={width}
        height={800}
        speed={1}
        backgroundColor={color.SKELETON_BACKGROUND}
        foregroundColor={color.SKELETON_FOREGROUND}
      >
        {/* MessageInfoHeader - Subject and date */}
        <Rect
          x={spacing}
          y={spacing}
          rx="4"
          ry="4"
          width={width * 0.7}
          height="18"
        />
        <Rect
          x={spacing}
          y={spacing + 25}
          rx="4"
          ry="4"
          width={width * 0.4}
          height="14"
        />

        {/* MessageInfoData - Avatar, username, email details */}
        <Circle cx={spacing + 20} cy={spacing + 70} r="20" />
        <Rect
          x={spacing + 50}
          y={spacing + 55}
          rx="4"
          ry="4"
          width={width * 0.3}
          height="14"
        />
        <Rect
          x={spacing + 50}
          y={spacing + 75}
          rx="4"
          ry="4"
          width={width * 0.5}
          height="12"
        />
        <Rect
          x={spacing + 50}
          y={spacing + 92}
          rx="4"
          ry="4"
          width={width * 0.4}
          height="12"
        />

        {/* Tab bar with 5 tabs - matches actual TabBarNavigator */}
        <Rect
          x="0"
          y={spacing + 130}
          rx="0"
          ry="0"
          width={width / 5}
          height="75"
        />
        <Rect
          x={width / 5}
          y={spacing + 130}
          rx="0"
          ry="0"
          width={width / 5}
          height="75"
        />
        <Rect
          x={(width / 5) * 2}
          y={spacing + 130}
          rx="0"
          ry="0"
          width={width / 5}
          height="75"
        />
        <Rect
          x={(width / 5) * 3}
          y={spacing + 130}
          rx="0"
          ry="0"
          width={width / 5}
          height="75"
        />
        <Rect
          x={(width / 5) * 4}
          y={spacing + 130}
          rx="0"
          ry="0"
          width={width / 5}
          height="75"
        />

        {/* Nested tabs (Details/Replies) */}
        <Rect
          x={spacing}
          y={spacing + 225}
          rx="4"
          ry="4"
          width={80}
          height="16"
        />
        <Rect
          x={width - spacing - 120}
          y={spacing + 225}
          rx="4"
          ry="4"
          width={120}
          height="16"
        />

        {/* Content area - simulating HTML content */}
        <Rect
          x={spacing}
          y={spacing + 265}
          rx="4"
          ry="4"
          width={width * 0.95}
          height="14"
        />
        <Rect
          x={spacing}
          y={spacing + 285}
          rx="4"
          ry="4"
          width={width * 0.8}
          height="14"
        />
        <Rect
          x={spacing}
          y={spacing + 305}
          rx="4"
          ry="4"
          width={width * 0.9}
          height="14"
        />
        <Rect
          x={spacing}
          y={spacing + 325}
          rx="4"
          ry="4"
          width={width * 0.75}
          height="14"
        />
        <Rect
          x={spacing}
          y={spacing + 345}
          rx="4"
          ry="4"
          width={width * 0.85}
          height="14"
        />
        <Rect
          x={spacing}
          y={spacing + 365}
          rx="4"
          ry="4"
          width={width * 0.7}
          height="14"
        />
        <Rect
          x={spacing}
          y={spacing + 385}
          rx="4"
          ry="4"
          width={width * 0.6}
          height="14"
        />

        {/* Bottom action buttons - Reply, Reply All, Forward */}
        <Rect x="0" y={750} rx="0" ry="0" width={width / 3} height="50" />
        <Rect
          x={width / 3}
          y={750}
          rx="0"
          ry="0"
          width={width / 3}
          height="50"
        />
        <Rect
          x={(width / 3) * 2}
          y={750}
          rx="0"
          ry="0"
          width={width / 3}
          height="50"
        />
      </ContentLoader>
    </View>
  );
};

const MessageHOC: React.FC = ({}: Props) => {
  const route = useRoute();
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const { width } = useWindowDimensions();
  const dispatch = useDispatch();
  const initialUMS_Guid = route.params?.UMS_Guid;
  const emailIds: string[] = route.params?.emailIds || [];
  const initialIndex = emailIds.findIndex((id) => id === initialUMS_Guid);
  const [currentEmailIndex, setCurrentEmailIndex] = useState(initialIndex);
  const [isInitialMount, setIsInitialMount] = useState(true);
  const flatListRef = useRef<FlatList>(null);
  const currentUMS_Guid = emailIds[currentEmailIndex];
  const { casesList } = useSelector((state) => state.persist.bTeamCasesSlice);
  const { color } = useThemeAwareObject((theme: Theme) => ({
    color: theme.color,
  }));
  const message: Message = useSelector(
    (state) => state.persist.gridMessageSlice.gridMessages.byId[currentUMS_Guid]
  );

  const messages = useSelector(
    (state) => state.persist.gridMessageSlice.gridMessages
  );

  const { token } = useSelector((state) => state.persist.bTeamAuth);
  const { attachments, downloadedAttachments } = useSelector(
    (state) => state.root.bTeamAttachmentsSlice
  );

  const {
    folders,
    selectedFolderId,
    getMessageBodyLoading,
    getMessageCommentsLoading,
    getMessageActionsLoading,
    getMessageBodyError,
    getMessageCommentsError,
    getMessageActionsError,
    copyOrMoveMessageFolderError,
    messageFolders,
  } = useSelector((state) => state.persist.gridMessageSlice);

  const { sendDraftMessageSuccess } = useSelector(
    (state) => state.root.bTeamGeneralSlice
  );
  const isDeletedFolder =
    folders.allIds.find(
      (id) => folders.byId[id].name === FOLDER_NAMES.deletedItems
    ) === selectedFolderId;

  const cases = useMemo(() => {
    return (
      message?.caseIds
        ?.map((id) => {
          const item = casesList.byId[id];

          return item
            ? {
                id: item.CAS_Guid,
                name: `${item.CAS_Reference} // ${item.CAS_Title}`,
                date: item.CAS_UpdatedTimestamp,
              }
            : null;
        })
        .filter(Boolean) || []
    );
  }, [message?.caseIds, casesList.byId]);

  const handleDeleteMessage = () => {
    dispatch(performDeleteAction({ ids: [currentUMS_Guid] }));
    navigation.goBack();
  };

  const handleUnDeleteMessage = () => {
    dispatch(performUnDeleteAction({ ids: [currentUMS_Guid] }));
    navigation.goBack();
  };

  const handleFlagMessageAction = () => {
    dispatch(
      flagMessages({
        ids: [currentUMS_Guid],
        value: message.isFlagged
          ? FLAGGED_API_VALUES.unFlag
          : FLAGGED_API_VALUES.flag,
      })
    );
  };

  const handleMarkAsReadUnreadAction = () => {
    // If message is viewed (read), mark as unread (mode 7)
    // If message is not viewed (unread), mark as read (mode 6)
    const mode = message?.isViewed ? 7 : 6;
    dispatch(performMarkAsReadUnreadAction({ ids: [currentUMS_Guid], mode }));
  };

  const handleGetMessageActions = () =>
    dispatch(getMessageActions({ UMS_Guid: currentUMS_Guid }));

  const setDownloadedAttachmentsAction = (payload) => {
    dispatch(setDownloadedAttachments(payload));
  };

  const clearDownloadedAttachmentsAction = () => {
    dispatch(clearDownloadedAttachments());
  };

  const clearCopyOrMoveMessageFolderErrorAction = () => {
    dispatch(clearCopyOrMoveMessageFolderError());
  };

  const handleMoveSelectedEmailsToFolder = () => {
    navigation.navigate(SCREEN_NAMES.folders, {
      emailCase: EMAIL_CASES.move,
      isMultiSelect: false,
      messageId: currentUMS_Guid,
    });
  };

  const handleCopySelectedEmailsToFolder = () => {
    navigation.navigate(SCREEN_NAMES.folders, {
      emailCase: EMAIL_CASES.copy,
      isMultiSelect: false,
      messageId: currentUMS_Guid,
    });
  };

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", () => {
      navigation.goBack();
      return true;
    });

    // Cleanup debounce timer on unmount
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [navigation]);

  useEffect(() => {
    navigation.setOptions({
      deleteMessage: isDeletedFolder
        ? handleUnDeleteMessage
        : handleDeleteMessage,
      handleFlagMessage: handleFlagMessageAction,
      handleMarkAsReadUnread: handleMarkAsReadUnreadAction,
    });
  }, [navigation, message, isDeletedFolder]);

  // Initial load for the current message (only on component mount)
  useEffect(() => {
    const initialUMS_Guid = emailIds[initialIndex];
    if (
      !messages.byId[initialUMS_Guid]?.fullMessageBody ||
      messages.byId[initialUMS_Guid]?.inOut === 3
    ) {
      dispatch(getMessageBody({ UMS_Guid: initialUMS_Guid }));
    }

    // dispatch(getMessageComments({ guid: initialUMS_Guid, entityId: 1001 }));
    // dispatch(getMessageMetadata({ guid: initialUMS_Guid }));
    // dispatch(getMessageActions({ UMS_Guid: initialUMS_Guid }));
  }, []); // Only run on mount

  // Handle initial scroll position and clear initial mount state
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsInitialMount(false);

      if (initialIndex > 0 && flatListRef.current) {
        flatListRef.current?.scrollToIndex({
          index: initialIndex,
          animated: false,
        });
      }
    }, 150); // Small delay to ensure FlatList is fully mounted

    return () => clearTimeout(timer);
  }, [initialIndex]);

  useFocusEffect(
    useCallback(() => {
      if (sendDraftMessageSuccess) dispatch(sendMessageClearStatus());
    }, [sendDraftMessageSuccess])
  );

  useEffect(() => {
    dispatch(
      performMarkAsReadUnreadAction({
        ids: [currentUMS_Guid],
        mode: 6,
      })
    );
  }, [currentUMS_Guid]);

  const repliesData = useMemo(
    () =>
      message?.repliedByUser
        ? [
            {
              id: 1,
              avatarName: calculateAvatarName(message?.repliedByUser),
              from: "",
              username: message?.repliedByUser,
              date: message?.timeReplied,
            },
          ]
        : [],
    [message?.repliedByUser, message?.timeReplied]
  );

  const handleFilesDownloadStatus = (
    attachmentId: string,
    downloadStatus: FileDownloadStatus
  ) => {
    setDownloadedAttachmentsAction({
      id: attachmentId,
      status: downloadStatus,
      isLoading: downloadStatus === FILE_DOWNLOAD_STATUS.loading,
    });
  };

  const handleDownloadAttachment = async (attachmentId: string) => {
    clearDownloadedAttachments();

    await downloadSingleAttachment(
      token,
      attachmentId,
      attachments?.byId?.[attachmentId]?.name,
      handleFilesDownloadStatus
    );
  };

  const handleDownloadAllAttachments = async (attachmentIds: string[]) => {
    // Clear any existing downloaded attachments.
    clearDownloadedAttachments();

    // If there are no attachments, we can optionally return or alert.
    if (attachmentIds.length === 0) {
      console.log("No attachments found");
      return;
    }

    const fileNamesMapping: Record<string, string> = {};
    if (attachments?.byId) {
      Object.keys(attachments.byId).forEach((id) => {
        // Ensure that the object has a "name" property.
        fileNamesMapping[id] = attachments.byId[id].name;
      });
    }

    // Download all attachments.
    await downloadMultipleAttachments(
      token,
      attachmentIds,
      fileNamesMapping,
      handleFilesDownloadStatus
    );
  };

  const isAnyFileDownloadLoading = useMemo(
    () =>
      Object.values(downloadedAttachments).some(
        (status) => status === FILE_DOWNLOAD_STATUS.loading
      ),
    [downloadedAttachments]
  );

  const handleEditDraftMessage = () => {
    navigation.navigate(SCREEN_NAMES.composeMessage, {
      UMS_Guid: currentUMS_Guid,
      messageType: MessageActionTypes.DraftEdit,
    });
  };

  const handleOnPressReply = () => {
    navigation.navigate(SCREEN_NAMES.composeMessage, {
      UMS_Guid: currentUMS_Guid,
      messageType: MessageActionTypes.Reply,
    });
  };

  const handleOnPressReplyAll = () => {
    navigation.navigate(SCREEN_NAMES.composeMessage, {
      UMS_Guid: currentUMS_Guid,
      messageType: MessageActionTypes.ReplyAll,
    });
  };

  const handleOnPressForward = () => {
    navigation.navigate(SCREEN_NAMES.composeMessage, {
      UMS_Guid: currentUMS_Guid,
      messageType: MessageActionTypes.Forward,
    });
  };

  const handleRetryMessageBody = () => {
    dispatch(getMessageBody({ UMS_Guid: currentUMS_Guid }));
  };

  // Debounced API call timer
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Debounced fetch for comments, metadata, actions, and preloading
  const debouncedFetchData = useCallback(
    (newIndex: number) => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
      debounceTimerRef.current = setTimeout(() => {
        const currentUMS_Guid = emailIds[newIndex];
        dispatch(getMessageComments({ guid: currentUMS_Guid, entityId: 1001 }));
        dispatch(getMessageMetadata({ guid: currentUMS_Guid }));
        dispatch(getMessageActions({ UMS_Guid: currentUMS_Guid }));

        // Preload adjacent messages
        const preload = (index: number) => {
          const id = emailIds[index];
          if (!messages.byId[id]?.fullMessageBody) {
            dispatch(getMessageBody({ UMS_Guid: id }));
          }
          dispatch(getMessageActions({ UMS_Guid: id }));
        };
        const prevIndex = newIndex - 1;
        const nextIndex = newIndex + 1;
        if (prevIndex >= 0) preload(prevIndex);
        if (nextIndex < emailIds.length) preload(nextIndex);
      }, 300);
    },
    [emailIds, messages.byId, dispatch]
  );

  const handleViewableItemsChanged = useCallback(
    ({ viewableItems }) => {
      if (viewableItems.length > 0) {
        const visibleItem = viewableItems[0];
        const newIndex = visibleItem.index;
        setCurrentEmailIndex(newIndex);

        const currentUMS_Guid = emailIds[newIndex];
        // Always fetch message body immediately on every swipe
        if (!messages.byId[currentUMS_Guid]?.fullMessageBody) {
          dispatch(getMessageBody({ UMS_Guid: currentUMS_Guid }));
        }

        // Defer all other actions to avoid spamming APIs on swipe
        // debouncedFetchData(newIndex);
      }
    },
    [emailIds, messages.byId, dispatch, debouncedFetchData]
  );

  const viewabilityConfig = {
    itemVisiblePercentThreshold: 50,
  };

  // Memoize static handler props to prevent unnecessary re-renders
  const staticHandlers = useMemo(
    () => ({
      moveToFolder: handleMoveSelectedEmailsToFolder,
      copyToFolder: handleCopySelectedEmailsToFolder,
      getMessageActions: handleGetMessageActions,
      handleRetryMessageBody,
      setDownloadedAttachments: setDownloadedAttachmentsAction,
      clearDownloadedAttachments: clearDownloadedAttachmentsAction,
      handleClearCopyOrMoveMessageFolderError:
        clearCopyOrMoveMessageFolderErrorAction,
      handleDownloadAttachment,
      handleDownloadAllAttachments,
      handleEditDraftMessage,
      onPressReply: handleOnPressReply,
      onPressReplyAll: handleOnPressReplyAll,
      onPressForward: handleOnPressForward,
    }),
    [
      handleMoveSelectedEmailsToFolder,
      handleCopySelectedEmailsToFolder,
      handleGetMessageActions,
      handleRetryMessageBody,
      setDownloadedAttachmentsAction,
      clearDownloadedAttachmentsAction,
      clearCopyOrMoveMessageFolderErrorAction,
      handleDownloadAttachment,
      handleDownloadAllAttachments,
      handleEditDraftMessage,
      handleOnPressReply,
      handleOnPressReplyAll,
      handleOnPressForward,
    ]
  );

  // Memoize dummy-specific props
  const dummySpecificProps = useMemo(
    () => ({
      comments: [],
      recipientsEmails: [],
      postNewComment: () => {},
      postMessageCommentLoading: false,
      postMessageCommentError: "",
      postMessageCommentSuccess: false,
      handlePostMessageCommentSuccessDismiss: () => {},
      handleStarComment: () => {},
      handleAddAttachment: () => {},
      uploadAttachmentLoading: false,
      uploadAttachmentError: "",
      attachmentsLength: 0,
      fetchNotifiedUsers: () => {},
      fetchNotifiedUsersLoading: false,
      fetchNotifiedUsersError: "",
    }),
    []
  );

  const renderMessageItem = ({ item: UMS_Guid, index }) => {
    const itemMessage = messages.byId[UMS_Guid];
    const isCurrentMessage = index === currentEmailIndex;

    // Show skeleton for missing messages OR if loading state is active
    if (
      !itemMessage ||
      !itemMessage.fullMessageBody ||
      (getMessageBodyLoading[UMS_Guid] && !itemMessage.fullMessageBody)
    ) {
      return <MessageSkeleton width={width} />;
    }

    // Common props used by both dummy and full messages
    const commonMessageProps = {
      width,
      message: itemMessage,
      replies: repliesData,
      messageAttachmentsIds: itemMessage?.attachmentsIds,
      attachments,
      attachmentsCount: itemMessage?.attachmentsCount,
      downloadedAttachments,
      foldersIds: itemMessage?.foldersIds,
      messageFolders,
      caseMetadata: itemMessage?.messageMetadata,
      cases,
      getMessageBodyLoading: getMessageBodyLoading[UMS_Guid] || false,
      getMessageCommentsLoading,
      getMessageActionsLoading,
      messageBodyError: getMessageBodyError,
      getMessageCommentsError,
      getMessageActionsError,
      copyOrMoveMessageFolderError,
      isAnyFileDownloadLoading,
      isDraft: itemMessage?.inOut === 3,
      ...staticHandlers,
    };

    // For dummy (non-current) messages, render simplified version without CommentsHOC
    if (!isCurrentMessage) {
      return (
        <View style={{ width }}>
          <MessageScreen
            {...commonMessageProps}
            {...dummySpecificProps}
            isDummy={true}
          />
        </View>
      );
    }

    // For current message, render full version with CommentsHOC
    return (
      <View style={{ width }}>
        <CommentsHOC
          guid={UMS_Guid}
          entityId="1001"
          umsGuidToSendComment={UMS_Guid}
          commentIds={itemMessage?.messageCommentIds}
          refreshListCallback={() =>
            dispatch(
              getMessageComments({
                guid: UMS_Guid,
                entityId: 1001,
              })
            )
          }
        >
          {React.createElement(MessageScreen, {
            ...commonMessageProps,
            isDummy: false,
          } as unknown as MessageScreenProps)}
        </CommentsHOC>
      </View>
    );
  };

  return (
    <TabCacheProvider>
      <FlatList
        style={{ flex: 1, backgroundColor: color.BACKGROUND }}
        ref={flatListRef}
        data={emailIds}
        renderItem={renderMessageItem}
        keyExtractor={(item) => item}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        initialScrollIndex={initialIndex}
        getItemLayout={(_, index) => ({
          length: width,
          offset: width * index,
          index,
        })}
        onScrollToIndexFailed={(info) => {
          const wait = new Promise((resolve) => setTimeout(resolve, 500));
          wait.then(() => {
            flatListRef.current?.scrollToIndex({
              index: info.index,
              animated: true,
            });
          });
        }}
        onViewableItemsChanged={handleViewableItemsChanged}
        viewabilityConfig={viewabilityConfig}
        maintainVisibleContentPosition={{
          minIndexForVisible: 0,
        }}
        // Optimized for smooth swiping - prevent white screens
        windowSize={31}
        maxToRenderPerBatch={8}
        updateCellsBatchingPeriod={300}
        removeClippedSubviews={false}
        initialNumToRender={emailIds.length < 15 ? emailIds.length : 15}
      />
    </TabCacheProvider>
  );
};

export default React.memo(MessageHOC);
