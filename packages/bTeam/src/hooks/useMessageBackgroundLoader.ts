import { useCallback, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { getMessageBody } from '../slices/gridMessageSlice';
import messageBackgroundService from '../services/MessageBackgroundService';

interface UseMessageBackgroundLoaderOptions {
  /**
   * Whether to automatically start background loading when component mounts
   */
  autoStart?: boolean;
  /**
   * Entity ID for comments (default: 1001)
   */
  entityId?: number;
}

interface UseMessageBackgroundLoaderReturn {
  /**
   * Load message body immediately (foreground)
   */
  loadMessageBodyImmediate: (messageId: string) => void;
  
  /**
   * Load non-essential message data in background
   */
  loadMessageDataBackground: (messageId: string, priority?: 'high' | 'medium' | 'low') => void;
  
  /**
   * Preload multiple messages in background
   */
  preloadMessages: (messageIds: string[], priority?: 'high' | 'medium' | 'low') => void;
  
  /**
   * Stop all background loading
   */
  stopBackgroundLoading: () => void;
  
  /**
   * Clear the background loading queue
   */
  clearBackgroundQueue: () => void;
  
  /**
   * Get current background loading status
   */
  getBackgroundStatus: () => { 
    queueLength: number; 
    isRunning: boolean; 
    currentTask: string | null;
  };
}

/**
 * Hook for managing message data loading with background processing
 */
export const useMessageBackgroundLoader = (
  options: UseMessageBackgroundLoaderOptions = {}
): UseMessageBackgroundLoaderReturn => {
  const dispatch = useDispatch();
  const { autoStart = true, entityId = 1001 } = options;

  /**
   * Load message body immediately (critical for UI)
   */
  const loadMessageBodyImmediate = useCallback((messageId: string) => {
    console.log(`⚡ Loading message body immediately: ${messageId}`);
    dispatch(getMessageBody({ UMS_Guid: messageId }));
  }, [dispatch]);

  /**
   * Load non-essential message data in background
   */
  const loadMessageDataBackground = useCallback((
    messageId: string, 
    priority: 'high' | 'medium' | 'low' = 'medium'
  ) => {
    console.log(`🔄 Queuing background data load for: ${messageId} (priority: ${priority})`);
    messageBackgroundService.addMessageToQueue(messageId, priority, entityId);
  }, [entityId]);

  /**
   * Preload multiple messages in background
   */
  const preloadMessages = useCallback((
    messageIds: string[], 
    priority: 'high' | 'medium' | 'low' = 'low'
  ) => {
    console.log(`📦 Preloading ${messageIds.length} messages in background`);
    messageBackgroundService.addMessagesToQueue(messageIds, priority, entityId);
  }, [entityId]);

  /**
   * Stop all background loading
   */
  const stopBackgroundLoading = useCallback(async () => {
    console.log('⏹️ Stopping background loading');
    await messageBackgroundService.stopBackgroundProcessing();
  }, []);

  /**
   * Clear the background loading queue
   */
  const clearBackgroundQueue = useCallback(() => {
    console.log('🗑️ Clearing background loading queue');
    messageBackgroundService.clearQueue();
  }, []);

  /**
   * Get current background loading status
   */
  const getBackgroundStatus = useCallback(() => {
    return messageBackgroundService.getQueueStatus();
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (autoStart) {
        // Clear queue when component unmounts to prevent unnecessary background work
        messageBackgroundService.clearQueue();
      }
    };
  }, [autoStart]);

  return {
    loadMessageBodyImmediate,
    loadMessageDataBackground,
    preloadMessages,
    stopBackgroundLoading,
    clearBackgroundQueue,
    getBackgroundStatus,
  };
};

export default useMessageBackgroundLoader;
