import React, { useState, useMemo, useCallback } from "react";
import { Pressable, StyleSheet, View } from "react-native";
import { RelatedUsersList } from "../../../../types/userMails";
import ContentLoader, { Rect } from "react-content-loader/native";

// Components
import { Theme, useThemeAwareObject, TabBarLabel } from "b-ui-lib";
import HtmlTab from "../HtmlTab/HtmlTab";
import FolderTab from "../FolderTab/FolderTab";
import CaseTab from "../CaseTab/CaseTab";
import CommentListTab from "../../../general/CommentListTab";
import AttachmentTab from "../../../general/AttachmentTab";
import { TEST_IDS } from "../../../../constants/testIds";
import {
  useBaseTabProps,
  useHtmlTabProps,
  useCommentTabProps,
  useAttachmentTabProps,
  useFolderTabProps,
  useCaseTabProps,
} from "../utils/propMemoization";

type Props = {
  width: number;
  replies: any;
  comments: any;
  messageAttachmentsIds: any;
  attachments: any;
  attachmentsCount: number;
  downloadedAttachments: number;
  foldersIds: [];
  messageFolders: any;
  caseMetadata: any;
  cases: any;
  recipientsEmails: RelatedUsersList;
  notifiedUsers: RelatedUsersList;
  html: string;
  getMessageBodyLoading: boolean;
  getMessageCommentsLoading: boolean;
  getMessageActionsLoading: boolean;
  getMessageCommentsError: string;
  getMessageActionsError: string;
  messageBodyError: string;
  copyOrMoveMessageFolderError: string;
  moveToFolder: () => void;
  copyToFolder: () => void;
  getMessageActions: () => void;
  handleRetryMessageBody: () => void;
  setDownloadedAttachments: (payload) => void;
  clearDownloadedAttachments: () => void;
  handleClearCopyOrMoveMessageFolderError: () => void;
  postNewComment: () => void;
  postMessageCommentLoading: boolean;
  postMessageCommentError: string;
  postMessageCommentSuccess: boolean;
  handlePostMessageCommentSuccessDismiss: () => void;
  handleStarComment: () => void;
  handleAddAttachment: () => void;
  uploadAttachmentLoading: boolean;
  uploadAttachmentError: string;
  attachmentsLength: number;
  handleDownloadAttachment: () => void;
  handleDownloadAllAttachments: () => void;
  isAnyFileDownloadLoading: boolean;
  isDraft: boolean;
  handleEditDraftMessage: () => void;
  fetchNotifiedUsers: () => void;
  fetchNotifiedUsersLoading: boolean;
  fetchNotifiedUsersError: string;
  onPressReply: () => void;
  onPressReplyAll: () => void;
  onPressForward: () => void;
};

// Tab content skeleton component for lazy loading
const TabContentSkeleton: React.FC<{ width: number }> = ({ width }) => {
  const { color } = useThemeAwareObject((theme: Theme) => ({
    color: theme.color,
  }));

  return (
    <View style={{ flex: 1, padding: 16 }}>
      <ContentLoader
        width={width - 32}
        height={400}
        speed={1}
        backgroundColor={color.SKELETON_BACKGROUND}
        foregroundColor={color.SKELETON_FOREGROUND}
      >
        <Rect x="0" y="0" rx="4" ry="4" width="100%" height="20" />
        <Rect x="0" y="35" rx="4" ry="4" width="80%" height="16" />
        <Rect x="0" y="60" rx="4" ry="4" width="90%" height="16" />
        <Rect x="0" y="85" rx="4" ry="4" width="75%" height="16" />
        <Rect x="0" y="120" rx="4" ry="4" width="100%" height="200" />
      </ContentLoader>
    </View>
  );
};

// Custom tab bar component
function CustomTabBar({ activeTab, onTabPress, tabs, color }) {
  return (
    <View style={{ flexDirection: "row", backgroundColor: "#61616B" }}>
      {tabs.map((tab, index) => {
        const isFocused = activeTab === index;

        return (
          <Pressable
            key={tab.key}
            accessibilityRole="button"
            accessibilityState={isFocused ? { selected: true } : {}}
            testID={tab.testID}
            onPress={() => onTabPress(index)}
            style={{
              flex: 1,
              justifyContent: "center",
              alignItems: "center",
              height: 75,
              backgroundColor: isFocused
                ? color.MESSAGE_ITEM__BACKGROUND
                : color.BORDER_EMAIL_FOLDER,
              borderWidth: 1,
              borderRightColor: color.BORDER_EMAIL_FOLDER,
              borderLeftColor: color.BORDER_EMAIL_FOLDER,
              borderBottomColor: color.MESSAGE_ITEM__BACKGROUND,
              borderTopWidth: 3,
              borderTopColor: isFocused
                ? color.MESSAGE_FLAG
                : color.BORDER_EMAIL_FOLDER,
              elevation: 0,
              shadowOpacity: 0,
            }}
          >
            {tab.label({
              focused: isFocused,
              onPress: () => onTabPress(index),
            })}
          </Pressable>
        );
      })}
    </View>
  );
}

const TabBarNavigator: React.FC<Props> = ({
  width,
  replies,
  comments,
  messageAttachmentsIds,
  attachments,
  attachmentsCount,
  downloadedAttachments,
  foldersIds,
  messageFolders,
  caseMetadata,
  cases,
  recipientsEmails,
  html,
  getMessageBodyLoading,
  getMessageCommentsLoading,
  getMessageActionsLoading,
  getMessageCommentsError,
  getMessageActionsError,
  messageBodyError,
  copyOrMoveMessageFolderError,
  moveToFolder,
  copyToFolder,
  getMessageActions,
  handleRetryMessageBody,
  setDownloadedAttachments,
  clearDownloadedAttachments,
  handleClearCopyOrMoveMessageFolderError,
  postNewComment,
  postMessageCommentLoading,
  postMessageCommentError,
  postMessageCommentSuccess,
  handlePostMessageCommentSuccessDismiss,
  handleStarComment,
  handleAddAttachment,
  uploadAttachmentLoading,
  uploadAttachmentError,
  attachmentsLength,
  handleDownloadAttachment,
  handleDownloadAllAttachments,
  isAnyFileDownloadLoading,
  isDraft,
  handleEditDraftMessage,
  fetchNotifiedUsers,
  fetchNotifiedUsersLoading,
  fetchNotifiedUsersError,
  notifiedUsers,
  onPressReply,
  onPressReplyAll,
  onPressForward,
}: Props) => {
  const [activeTab, setActiveTab] = useState(0);
  const [loadedTabs, setLoadedTabs] = useState<Set<number>>(new Set([0])); // Start with first tab loaded
  const [tabLoadingStates, setTabLoadingStates] = useState<Set<number>>(
    new Set()
  );
  const { color } = useThemeAwareObject(createStyles);

  // Create base props object that's shared across all tabs
  const baseTabProps = useBaseTabProps(
    width,
    {
      getMessageBodyLoading,
      getMessageActionsLoading,
      getMessageCommentsLoading,
      postMessageCommentLoading,
      uploadAttachmentLoading,
      fetchNotifiedUsersLoading,
      isAnyFileDownloadLoading,
    },
    {
      getMessageActionsError,
      getMessageCommentsError,
      messageBodyError,
      copyOrMoveMessageFolderError,
      postMessageCommentError,
      uploadAttachmentError,
      fetchNotifiedUsersError,
    },
    {
      isDraft,
      postMessageCommentSuccess,
      attachmentsLength,
    }
  );

  // Memoize individual tab props
  const htmlTabProps = useHtmlTabProps(baseTabProps, html, replies);
  const commentTabProps = useCommentTabProps(
    baseTabProps,
    comments,
    recipientsEmails,
    notifiedUsers
  );
  const attachmentTabProps = useAttachmentTabProps(
    baseTabProps,
    messageAttachmentsIds,
    attachments,
    attachmentsCount,
    downloadedAttachments
  );
  const folderTabProps = useFolderTabProps(
    baseTabProps,
    foldersIds,
    messageFolders
  );
  const caseTabProps = useCaseTabProps(baseTabProps, caseMetadata, cases);

  // Define tabs configuration with lazy loading and memoized props
  const tabs = useMemo(
    () => [
      {
        key: "html-body",
        testID: undefined,
        label: ({ focused, onPress }) => (
          <TabBarLabel iconName="mail" focused={focused} onPress={onPress} />
        ),
        component: () => (
          <HtmlTab
            html={html}
            width={width}
            replies={replies}
            getMessageBodyLoading={getMessageBodyLoading}
            getMessageActionsLoading={getMessageActionsLoading}
            handleRefreshList={getMessageActions}
            getMessageActionsError={getMessageActionsError}
            messageBodyError={messageBodyError}
            handleRetryMessageBody={handleRetryMessageBody}
            isDraft={isDraft}
            handleEditDraftMessage={handleEditDraftMessage}
            onPressReply={onPressReply}
            onPressReplyAll={onPressReplyAll}
            onPressForward={onPressForward}
          />
        ),
      },
      {
        key: "comments-list",
        testID: undefined,
        label: ({ focused, onPress }) => (
          <TabBarLabel
            iconName="message"
            focused={focused}
            count={comments?.length}
            onPress={onPress}
          />
        ),
        component: () => (
          <CommentListTab
            comments={comments}
            recipientsEmails={recipientsEmails}
            handlePostNewComment={postNewComment}
            handleStarComment={handleStarComment}
            postMessageCommentLoading={postMessageCommentLoading}
            postMessageCommentError={postMessageCommentError}
            postMessageCommentSuccess={postMessageCommentSuccess}
            handlePostMessageCommentSuccessDismiss={
              handlePostMessageCommentSuccessDismiss
            }
            handleAddAttachment={handleAddAttachment}
            uploadAttachmentLoading={uploadAttachmentLoading}
            uploadAttachmentError={uploadAttachmentError}
            attachmentsLength={attachmentsLength}
            attachments={attachments}
            handleDownloadAttachment={handleDownloadAttachment}
            handleDownloadAllAttachments={handleDownloadAllAttachments}
            notifiedUsers={notifiedUsers}
            fetchNotifiedUsers={fetchNotifiedUsers}
            fetchNotifiedUsersLoading={fetchNotifiedUsersLoading}
            fetchNotifiedUsersError={fetchNotifiedUsersError}
            width={width}
            isLoading={getMessageCommentsLoading}
          />
        ),
      },
      {
        key: "attachment-list",
        testID: TEST_IDS.messageAttachmentTabTabBarIcon,
        label: ({ focused, onPress }) => (
          <TabBarLabel
            testID={TEST_IDS.messageAttachmentTabTabBarIcon}
            iconName="paperclip"
            focused={focused}
            count={attachmentsCount?.toString()}
            onPress={onPress}
          />
        ),
        component: () => (
          <AttachmentTab
            attachmentsIds={messageAttachmentsIds}
            messageAttachments={attachments}
            attachmentsCount={attachmentsCount}
            downloadedAttachments={downloadedAttachments}
            handleRefreshList={getMessageActions}
            getMessageActionsError={getMessageActionsError}
            getMessageActionsLoading={getMessageActionsLoading}
            clearDownloadedAttachments={clearDownloadedAttachments}
            handleDownloadAttachment={handleDownloadAttachment}
            handleDownloadAllAttachments={handleDownloadAllAttachments}
            isAnyFileDownloadLoadin={isAnyFileDownloadLoading}
          />
        ),
      },
      {
        key: "folder-list",
        testID: undefined,
        label: ({ focused, onPress }) => (
          <TabBarLabel
            iconName="folder"
            focused={focused}
            count={foldersIds?.length}
            onPress={onPress}
          />
        ),
        component: () => (
          <FolderTab
            foldersIds={foldersIds}
            messageFolders={messageFolders}
            moveToFolder={moveToFolder}
            copyToFolder={copyToFolder}
            isLoading={getMessageActionsLoading}
            handleRefreshList={getMessageActions}
            copyOrMoveMessageFolderError={copyOrMoveMessageFolderError}
            getMessageActionsError={getMessageActionsError}
            handleClearCopyOrMoveMessageFolderError={
              handleClearCopyOrMoveMessageFolderError
            }
            width={width}
          />
        ),
      },
      {
        key: "cases-list",
        testID: undefined,
        label: ({ focused, onPress }) => (
          <TabBarLabel iconName="info" focused={focused} onPress={onPress} />
        ),
        component: () => (
          <CaseTab
            caseMetadata={caseMetadata}
            cases={cases}
            width={width}
            isLoading={getMessageActionsLoading}
          />
        ),
      },
    ],
    [
      htmlTabProps,
      commentTabProps,
      attachmentTabProps,
      folderTabProps,
      caseTabProps,
    ]
  );

  // Handle tab press with lazy loading
  const handleTabPress = useCallback(
    (tabIndex: number) => {
      if (tabIndex === activeTab) return;

      setActiveTab(tabIndex);

      // Start loading tab if not already loaded
      if (!loadedTabs.has(tabIndex)) {
        setTabLoadingStates((prev) => new Set(prev).add(tabIndex));

        // Simulate brief loading delay to show loading state
        setTimeout(() => {
          setLoadedTabs((prev) => new Set(prev).add(tabIndex));
          setTabLoadingStates((prev) => {
            const newSet = new Set(prev);
            newSet.delete(tabIndex);
            return newSet;
          });
        }, 100);
      }
    },
    [activeTab, loadedTabs]
  );

  // Render tab content with lazy loading
  const renderTabContent = useCallback(() => {
    const isTabLoading = tabLoadingStates.has(activeTab);
    const isTabLoaded = loadedTabs.has(activeTab);

    if (isTabLoading || !isTabLoaded) {
      return <TabContentSkeleton width={width} />;
    }

    return tabs[activeTab].component();
  }, [activeTab, tabLoadingStates, loadedTabs, width, tabs]);

  return (
    <View style={{ flex: 1 }}>
      <CustomTabBar
        activeTab={activeTab}
        onTabPress={handleTabPress}
        tabs={tabs}
        color={color}
      />
      <View style={{ flex: 1 }}>{renderTabContent()}</View>
    </View>
  );
};

const MemoizedTabBarNavigator = React.memo(TabBarNavigator);
export default MemoizedTabBarNavigator;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
  });

  return { styles, color };
};
