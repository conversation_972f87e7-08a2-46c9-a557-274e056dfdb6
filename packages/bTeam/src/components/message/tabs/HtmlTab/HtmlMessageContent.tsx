import React, { memo } from "react";
import {
  ScrollView,
  StyleSheet,
  View,
} from "react-native";
import RenderHtml from "react-native-render-html";

// Components
import {
  FONT_SIZES,
  ICON_POSITIONS,
  IconTextButton,
  SPACING,
  Theme,
  useThemeAwareObject,
} from "b-ui-lib";
import TabErrorMessage from "../../TabErrorMessage";

type Props = {
  html: string;
  width: number;
  messageBodyError: string;
  handleRetryMessageBody: () => void;
};

const HtmlMessageContent = memo<Props>(({
  html,
  width,
  messageBodyError,
  handleRetryMessageBody,
}) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  if (messageBodyError) {
    return (
      <View style={styles.errorContainer}>
        <TabErrorMessage
          text={messageBodyError}
          isVisible={Boolean(messageBodyError)}
        />

        <IconTextButton
          iconPosition={ICON_POSITIONS.left}
          iconName="Redo"
          iconSize={20}
          iconColor={color.BRAND_BLUE}
          title="Retry"
          onPress={handleRetryMessageBody}
          textStyle={styles.retryButtonText}
          containerStyle={styles.retryButton}
        />
      </View>
    );
  }

  return (
    <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
      <RenderHtml
        contentWidth={width}
        source={{ html }}
        enableExperimentalMarginCollapsing={true}
        computeEmbeddedMaxWidth={(contentWidth) => contentWidth}
        ignoredDomTags={['map', 'area']}
        renderersProps={{
          img: {
            enableExperimentalPercentWidth: true,
          },
        }}
        defaultTextProps={{
          style: styles.htmlTextStyle,
        }}
        // Performance optimizations
        rendererProps={{
          blockquote: {
            htmlAttribs: {
              style: 'margin: 16px 0; padding: 16px; border-left: 4px solid #ddd; background: #f9f9f9;'
            }
          }
        }}
        tagsStyles={{
          body: {
            fontSize: FONT_SIZES.FOURTEEN,
            lineHeight: 20,
          },
          p: {
            marginBottom: 12,
          },
          div: {
            marginBottom: 8,
          },
        }}
        systemFonts={['System']}
      />
    </ScrollView>
  );
});

HtmlMessageContent.displayName = 'HtmlMessageContent';

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    scrollContainer: {
      flex: 1,
    },
    htmlTextStyle: {
      color: color.TEXT_DEFAULT,
      fontSize: FONT_SIZES.FOURTEEN,
      lineHeight: 20,
    },
    errorContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingVertical: SPACING.L,
    },
    retryButton: {
      marginTop: SPACING.M,
      paddingHorizontal: SPACING.L,
      paddingVertical: SPACING.S,
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: color.BRAND_BLUE,
    },
    retryButtonText: {
      fontSize: FONT_SIZES.FOURTEEN,
      fontWeight: "600",
      color: color.BRAND_BLUE,
    },
  });

  return { styles, color };
};

export default HtmlMessageContent;