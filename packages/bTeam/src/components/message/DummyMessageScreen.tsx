import React from "react";
import { StyleSheet, View } from "react-native";
import { Message } from "../../types/message";
import {
  MessageInfoData,
  MessageInfoHeader,
  SPACING,
  Theme,
  useThemeAwareObject,
} from "b-ui-lib";
import { getEmailsString } from "../../helpers/getEmailsString";

// Components
import HtmlTab from "./tabs/HtmlTab/HtmlTab";
import DummyTabBar from "./DummyTabBar";

type Props = {
  width: number;
  message: Message;
  replies: any;
  getMessageBodyLoading: boolean;
  getMessageActionsLoading: boolean;
  messageBodyError: string;
  getMessageActionsError: string;
  getMessageActions: () => void;
  handleRetryMessageBody: () => void;
  isDraft: boolean;
  handleEditDraftMessage: () => void;
  onPressReply: () => void;
  onPressReplyAll: () => void;
  onPressForward: () => void;
};

const MemoizedMessageInfoHeader = React.memo(MessageInfoHeader);
const MemoizedMessageInfoData = React.memo(MessageInfoData);

const DummyMessageScreen = ({
  width,
  message,
  replies,
  getMessageBodyLoading,
  getMessageActionsLoading,
  messageBodyError,
  getMessageActionsError,
  getMessageActions,
  handleRetryMessageBody,
  isDraft,
  handleEditDraftMessage,
  onPressReply,
  onPressReplyAll,
  onPressForward,
}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  const tabIcons = ["message", "paperclip", "folder", "info"];

  return (
    <View style={styles.container}>
      <MemoizedMessageInfoHeader
        subject={message?.subject}
        sentDate={message?.sentDate}
        containerStyle={styles.messageInfoContainer}
      />

      <MemoizedMessageInfoData
        avatarName={message?.avatarName}
        inOut={message?.inOut}
        username={message?.username}
        from={message?.from}
        tos={getEmailsString(message?.tos)}
        ccs={getEmailsString(message?.ccs)}
        bccs={getEmailsString(message?.bccs)}
        containerStyle={styles.messageInfoContainer}
      />

      <DummyTabBar tabIcons={tabIcons} />

      <HtmlTab
        html={message?.fullMessageBody}
        width={width}
        replies={replies}
        getMessageBodyLoading={getMessageBodyLoading}
        getMessageActionsLoading={getMessageActionsLoading}
        handleRefreshList={getMessageActions}
        getMessageActionsError={getMessageActionsError}
        messageBodyError={messageBodyError}
        handleRetryMessageBody={handleRetryMessageBody}
        isDraft={isDraft}
        handleEditDraftMessage={handleEditDraftMessage}
        onPressReply={onPressReply}
        onPressReplyAll={onPressReplyAll}
        onPressForward={onPressForward}
      />
    </View>
  );
};

export default React.memo(DummyMessageScreen);

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
      flex: 1,
    },
    messageInfoContainer: {
      padding: SPACING.M,
    },
  });

  return { styles, color };
};
