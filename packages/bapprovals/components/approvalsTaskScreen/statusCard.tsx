import React from "react";
import { StyleSheet, View } from "react-native";
import dayjs from "dayjs";

// Components
import { CustomText, TitleGroupedCards } from "bcomponents";
import TaskStatusIcon from "./statusIcon";

// Styles
import { SPACING } from "bstyles";
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";

type Props = {
  supplier: string | undefined;
  date: string | Date | undefined;
  taskStatus: 0 | 1 | 2 | 3 | 4 | 5 | 6 | undefined | null;
};

const StatusCard = ({ supplier, date, taskStatus }: Props) => {
  const { styles } = useThemeAwareObject(createStyles);

  return (
    <View
      style={{
        flexDirection: "row",
        justifyContent: "space-between",
        marginBottom: SPACING.XS,
      }}
    >
      <View style={{ flex: 1 }}>
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <TitleGroupedCards title="Supplier:" />

          <CustomText style={{ fontWeight: "700" }}>{supplier}</CustomText>
        </View>

        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
          }}
        >
          <View
            style={{
              flex: 1,
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <TitleGroupedCards title="Registration date:" />

            <CustomText style={{ fontWeight: "700" }}>
              {dayjs(date).format("DD/MM/YYYY")}
            </CustomText>
          </View>
        </View>
      </View>
    </View>
  );
};

export default StatusCard;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    iconWrapper: {
      justifyContent: "center",
      alignItems: "center",
      marginBottom: SPACING.S,
    },
  });

  return { styles, color };
};
