import React from "react";
import { StyleSheet, View } from "react-native";
import dayjs from "dayjs";

// Components
import { CustomText } from "bcomponents";

// Styles
import { SPACING } from "bstyles";
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";

type Props = {
  title: string;
  date: string | Date | undefined;
};

const DateCard = ({ title, date }: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <View style={styles.container}>
      <View style={styles.row}>
        <CustomText style={{}}>{title}:</CustomText>
        <CustomText style={{ fontWeight: "700" }}>
          {dayjs(date).isValid() ? dayjs(date).format("DD/MM/YYYY") : "-"}
        </CustomText>
      </View>
    </View>
  );
};

export default DateCard;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      borderRadius: SPACING.SIX,
    },
    row: {
      flexDirection: "row",
      justifyContent: "space-between",
      paddingVertical: SPACING.XXS,
    },
  });

  return { styles, color };
};
